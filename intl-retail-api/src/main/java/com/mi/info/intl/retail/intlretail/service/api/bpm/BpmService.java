package com.mi.info.intl.retail.intlretail.service.api.bpm;

import com.mi.info.intl.retail.intlretail.service.api.bpm.dto.BpmDetail;
import com.mi.info.intl.retail.intlretail.service.api.bpm.dto.FormDataEntity;
import com.mi.info.intl.retail.intlretail.service.api.bpm.enums.BpmApproveBusinessCodeEnum;

import java.util.Map;

public interface BpmService {

    //BPM_CREATE_LOG:BaseResp(code=0, message=success, data=CreateProcInstResp(businessKey=efb3095a6d734b76b408ca98cf72a52a),
    // logId=4631fde1d0f1d774e6d6018b909d970d, traceId=4789133d48a6014fef4d8f41e189ba65)
    String create(FormDataEntity formDataEntity, BpmApproveBusinessCodeEnum businessCode, String emailPrefix,
                String businessKey, Map<String, Object> variables);

    void terminate(String businessKey, String emailPrefix, String comment);

    void recall(String businessKey, String emailPrefix, String comment);

    BpmDetail detail(String businessKey);

    void submit(String taskId, String operator, FormDataEntity formDataEntity, Map<String, Object> variables);
}
