package com.mi.info.intl.retail.intlretail.service.api.masterdata.dto;

import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 新增明文用户
 *
 * <AUTHOR>
 * @date 2025/7/31
 **/
@Data
public class AddPlainTextImeiUserRequest implements Serializable {
    
    private static final long serialVersionUID = -3241973778489085918L;

    /**
     * miId
     */
    @ApiDocClassDefine(value = "miId")
    private Long miId;

    /**
     * 图片id,最多上传三张图片ID
     */
    @ApiDocClassDefine(value = "图片id,最多上传三张图片ID")
    private List<FileData> fileDataList;

    @Data
    public static class FileData implements Serializable {

        private static final long serialVersionUID = -4698926369490849965L;
        /**
         * 图片id
         */
        private Long fileId;

        /**
         * 图片url
         */
        private String url;
    }
}




