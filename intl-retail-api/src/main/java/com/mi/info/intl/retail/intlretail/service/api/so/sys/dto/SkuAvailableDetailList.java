package com.mi.info.intl.retail.intlretail.service.api.so.sys.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class SkuAvailableDetailList implements Serializable {
    private static final long serialVersionUID = -7725587885114317779L;
    @ExcelProperty(index = 0, value = "ID")
    private Long id;

    @ExcelProperty(index = 1, value = "Product ID")
    private String productId;

    @ExcelProperty(index = 2, value = "SKU")
    private String skuId;

    @ExcelProperty(index = 3, value = "SKU Name")
    private String skuName;

    @ExcelProperty(index = 4, value = "Short Name")
    private String shortname;

    @ExcelProperty(index = 5, value = "Model Level")
    private String modelLevel;

    @ExcelProperty(index = 6, value = "69 Code")
    private String code69;
    @ExcelIgnore
    private Integer isSn;
    @ExcelProperty(index = 7, value = "Is SN")
    private String isSnDesc;
    @ExcelIgnore
    private Integer is69;
    @ExcelProperty(index = 8, value = "Is 69")
    private String is69Desc;

    @ExcelProperty(index = 9, value = "SPU ID")
    private String spuId;

    @ExcelProperty(index = 10, value = "SPU EN")
    private String spuEn;

    @ExcelProperty(index = 11, value = "Category EN")
    private String categoryEn;

    @ExcelProperty(index = 12, value = "Product Line")
    private String productLine;

    @ExcelProperty(index = 13, value = "Product Line EN")
    private String productLineEn;

    @ExcelProperty(index = 14, value = "Country")
    private String countryName;

    @ExcelProperty(index = 15, value = "Country Code")
    private String countryCode;

    @ExcelProperty(index = 16, value = "Created Time")
    private String deliverytime;

    @ExcelProperty(index = 17, value = "Sales Time")
    private String salestime;
    @ExcelIgnore
    private Integer status;
    @ExcelProperty(index = 18, value = "Sales Status")
    private String statusDesc;

}