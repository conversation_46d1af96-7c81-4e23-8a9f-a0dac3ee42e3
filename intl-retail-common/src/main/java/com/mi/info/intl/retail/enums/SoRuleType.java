package com.mi.info.intl.retail.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum SoRuleType {
    /**
     * 类型, 1-so规则, 2-ldu规则, 后续可以扩展
     */
    NONE(0, "无"),
    SO_RULE(1, "so规则,"),
    LDU_ROLE(2, "ldu规则,"),
    ;

    @JsonValue
    private int code;
    private String desc;

    @JsonCreator
    public static SoRuleType fromCode(Integer type) {
        if (null == type) {
            throw new IllegalArgumentException("type cannot be  null");
        }
        for (SoRuleType soRuleType : SoRuleType.values()) {
            if (Objects.equals(soRuleType.getCode(), type)) {
                return soRuleType;
            }
        }
        return SoRuleType.NONE;
    }
}
