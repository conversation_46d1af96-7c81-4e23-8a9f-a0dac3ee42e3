package com.mi.info.intl.retail.core.feishu.service;

import com.mi.info.intl.retail.core.feishu.bean.message.SendMessageResp;

import java.util.List;

public interface SendMessageService {

    /**
     * 指定岗位下所有人发消息
     *
     * @param message 信息
     * @param positionId 位置ID
     */
    void sendTextMessageToPosition(String message, String positionId);


    SendMessageResp sendGroupTextMessage(String groupId, String email, String content);

    void sendPlainMessageWithFeiShu(String token, String message, List<String> idList, String openId);

    /**
     * 向Fei Shu发送简单消息
     *
     * @param message 信息
     * @param idList 用户ID
     * @param openId 类型,open_id指普通用户，chat_id指群聊
     */
    void sendPlainMessageWithFeiShu(String message, List<String> idList, String openId);
}
