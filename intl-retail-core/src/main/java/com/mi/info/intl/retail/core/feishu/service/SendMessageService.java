package com.mi.info.intl.retail.core.feishu.service;

import java.util.List;

public interface SendMessageService {

    /**
     * 指定岗位下所有人发消息
     *
     * @param message 信息
     * @param positionId 位置ID
     */
    void sendTextMessageToPosition(String message, String positionId);

    /**
     * 向Fei Shu发送简单消息
     *
     * @param token 令牌
     * @param message 信息
     * @param idList 用户ID
     * @param openId 类型,open_id指普通用户，chat_id指群聊
     */
    void sendPlainMessageWithFeiShu(String token, String message, List<String> idList, String openId);

    /**
     * 向<PERSON>i Shu发送简单消息
     *
     * @param message 信息
     * @param idList 用户ID
     * @param openId 类型,open_id指普通用户，chat_id指群聊
     */
    void sendPlainMessageWithFeiShu(String message, List<String> idList, String openId);
}
