package com.mi.info.intl.retail.core.feishu.service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.mi.info.intl.retail.core.feishu.bean.config.FeiShuConfiguration;
import com.mi.info.intl.retail.core.feishu.bean.message.SendMessageReq;
import com.mi.info.intl.retail.core.feishu.bean.message.SendMessageResp;
import com.mi.info.intl.retail.core.feishu.bean.token.GetFeiShuTokenReq;
import com.mi.info.intl.retail.core.feishu.bean.token.GetFeiShuTokenResp;
import com.mi.info.intl.retail.core.feishu.bean.user.GetUserInfoListReq;
import com.mi.info.intl.retail.core.feishu.bean.user.GetUserInfoListResp;
import com.mi.info.intl.retail.core.org.configuration.JobInfo;
import com.mi.info.intl.retail.core.org.configuration.OrganizationPlatformConf;
import com.mi.info.intl.retail.core.org.service.OrganizePlatformService;
import com.mi.info.intl.retail.core.utils.EscapedUtils;
import com.mi.info.intl.retail.http.HttpClient;
import com.xiaomi.nr.eiam.admin.vo.provider.user.UserSensitiveInfoResp;
import com.xiaomi.nr.eiam.api.dto.userinfo.UserPosition;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

@Slf4j
@Service
public class SendMessageServiceImpl implements SendMessageService {

    @Resource
    private FeiShuConfiguration feiShuConfiguration;

    @Resource
    private OrganizationPlatformConf organizationPlatformConf;

    @Resource
    private OrganizePlatformService organizePlatformService;

    @Override
    public void sendTextMessageToPosition(String message, String positionId) {
        GetFeiShuTokenResp tokenResp = getToken();
        List<GetUserInfoListResp.DataDTO.UserListDTO> userList =
            getUserInfoList(tokenResp.getTenantAccessToken(), positionId);
        sendPlainMessageWithFeiShu(tokenResp.getTenantAccessToken(), message,
            userList.stream().map(GetUserInfoListResp.DataDTO.UserListDTO::getUserId).collect(Collectors.toList()),
            "open_id");
    }

    public GetFeiShuTokenResp getToken() {
        GetFeiShuTokenReq req = GetFeiShuTokenReq.builder().appId(feiShuConfiguration.getAppId())
            .appSecret(feiShuConfiguration.getAppSecret()).build();
        return HttpClient.textBody(feiShuConfiguration.getTokenUrl()).json(req).asBean(GetFeiShuTokenResp.class);
    }

    /**
     * 获取用户信息列表
     *
     * @param token 令牌
     * @param positionId 岗位id
     * @return {@link List }<{@link String }>
     */
    public List<GetUserInfoListResp.DataDTO.UserListDTO> getUserInfoList(String token, String positionId) {
        // 获取国家策略运营
        JobInfo info = organizationPlatformConf.getJobInfoCopy(positionId);
        List<UserPosition> list = organizePlatformService.getOrganizePlatform(info);
        List<UserSensitiveInfoResp> userInfoRespList = organizePlatformService.getBatchUserInfo(
            list.stream().map(UserPosition::getMiId).collect(Collectors.toList()),
            info.getScene() == null ? organizationPlatformConf.getScene() : info.getScene());
        if (CollectionUtils.isEmpty(userInfoRespList)) {
            return Lists.newArrayList();
        }
        GetUserInfoListReq userInfoListReq = new GetUserInfoListReq();
        userInfoListReq
            .setEmails(userInfoRespList.stream().map(UserSensitiveInfoResp::getEmail).collect(Collectors.toList()));

        GetUserInfoListResp userInfoListResp = HttpClient.textBody(feiShuConfiguration.getGetUnionidsUrl())
            .header("Authorization", "Bearer " + token).json(userInfoListReq).asBean(GetUserInfoListResp.class);

        if (userInfoListResp.getCode() != 0 || CollectionUtils.isEmpty(userInfoListResp.getData().getUserList())) {
            return Lists.newArrayList();
        }
        return userInfoListResp.getData().getUserList();
    }

    /**
     * 发送消息
     *
     * @param token 令牌
     * @param message 信息
     * @param userIds 用户id
     * @param openId open_id指普通用户，chat_id指群聊
     */
    @Override
    public void sendPlainMessageWithFeiShu(String token, String message, List<String> userIds, String openId) {
        if (CollectionUtils.isEmpty(userIds)) {
            log.warn("message send fail, user list is empty");
            return;
        }
        SendMessageReq messageReq = new SendMessageReq();
        SendMessageReq.TextContent content = new SendMessageReq.TextContent();
        content.setText(message);
        messageReq.setContent(JSON.toJSONString(content));
        messageReq.setMsgType("text");
        for (String userId : userIds) {
            messageReq.setReceiveId(userId);
            SendMessageResp resp =
                HttpClient.textBody(feiShuConfiguration.getMessageUrl()).header("Authorization", "Bearer " + token)
                    .queryString("receive_id_type", openId).json(messageReq).asBean(SendMessageResp.class);
            log.info("send message to userId:{}, resp:{}", userId, resp);

        }
    }

    @Override
    public void sendPlainMessageWithFeiShu(String message, List<String> userIds, String openId) {
        GetFeiShuTokenResp tokenResp = getToken();
        sendPlainMessageWithFeiShu(tokenResp.getTenantAccessToken(), message, userIds, openId);
    }


    @Override
    public SendMessageResp sendGroupTextMessage(String groupId, String email, String content) {
        SendMessageReq request = new SendMessageReq();
        request.setReceiveId(groupId);
        request.setMsgType("text");
        if (StringUtils.isBlank(email)) {
            content = String.format("{\"text\":\"%s\"}", EscapedUtils.replaceEscapedCharacters(content));
        } else {
            content = String.format("{\"text\":\"<at user_id=\\\"%s\\\"></at> %s\"}", email, EscapedUtils.replaceEscapedCharacters(content));
        }

        request.setContent(content);
        String token = this.getToken().getTenantAccessToken();
        SendMessageResp resp =
                HttpClient.textBody("https://open.f.mioffice.cn/open-apis/im/v1/messages?receive_id_type=chat_id").header("Content-Type",
                        "application/json").header("Authorization",
                        "Bearer " + token).json(request).asBean(SendMessageResp.class);
        return resp;
    }
}
