package com.mi.info.intl.retail.ldu.dto;

import com.mi.info.intl.retail.api.so.rule.resp.GetRetailRuleResp;
import lombok.Data;

import java.util.Objects;

@Data
public class GetRetailLduResp extends GetRetailRuleResp {
    private String ldu; //TODO

    @Override
    public boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (!(obj instanceof GetRetailLduResp)) {
            return false;
        }
        //
        GetRetailLduResp fobj = (GetRetailLduResp) obj;
        // TODO 其他字段也要参与比较（如果有）
        return fobj.ldu.equals((fobj.getLdu()));
    }

    @Override
    public int hashCode() {
        return Objects.hash(ldu); // 与equals保持一致的哈希计算
    }
}
