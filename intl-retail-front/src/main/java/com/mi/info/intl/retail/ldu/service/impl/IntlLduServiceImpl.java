package com.mi.info.intl.retail.ldu.service.impl;

import com.mi.info.intl.retail.api.so.rule.req.GetRetailerSoRuleReq;
import com.mi.info.intl.retail.api.so.rule.resp.GetRetailRuleResp;
import com.mi.info.intl.retail.api.so.rule.service.IntlRuleRetailerApiService;
import com.mi.info.intl.retail.enums.SoRuleType;
import com.mi.info.intl.retail.ldu.dto.GetRetailLduResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service("lduRuleService") // 指定bean名称
public class IntlLduServiceImpl implements IntlRuleRetailerApiService {

    /**
     * TODO
     * 返回LDU规则对应的信息
     *
     * 获取零售商ldu规则
     * @param req req
     * @return {@link GetRetailLduResp }
     */
    @Override
    public GetRetailLduResp getRetailerRule(GetRetailerSoRuleReq req) {
        // TODO
        GetRetailLduResp resp = new GetRetailLduResp();
        resp.setRoleType(SoRuleType.LDU_ROLE); //设置类型为LDU_ROLE
        return resp;
    }

    @Override
    public String getType() {
        return "lduRuleService";
    }
}
