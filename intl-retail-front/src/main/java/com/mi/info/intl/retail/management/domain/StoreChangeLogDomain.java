package com.mi.info.intl.retail.management.domain;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 门店变更日志领域对象
 */
@Data
public class StoreChangeLogDomain implements Serializable {
    private Integer id;
    private String storeName;
    private String storeCode;
    private Integer positionChangeId;
    private String rmsStoreCode;
    private Date effectiveTime;
    private Integer storeClass;
    private Integer workableType;
    private Integer hasPc;
    private Integer hasSr;
    private Integer storeStatus;
    private String changeReason;
    private Date recordCreateTime;
    private Integer storeGrade;
    private Integer storeType;
    private Integer storeChannelType;
    private Integer hasFront;
    private Integer hasPos;
    private Integer remainingXiaomiStore;
    private Integer effectiveStatus;
}
