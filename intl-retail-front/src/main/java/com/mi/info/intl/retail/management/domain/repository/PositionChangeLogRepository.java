package com.mi.info.intl.retail.management.domain.repository;

import com.mi.info.intl.retail.management.domain.PositionChangeLogDomain;

import java.util.Date;
import java.util.List;

/**
 * 岗位变更日志仓储接口
 */
public interface PositionChangeLogRepository {
    boolean save(PositionChangeLogDomain positionChangeLogDomain);
    PositionChangeLogDomain getById(Integer id);
    List<PositionChangeLogDomain> listAll();
    PositionChangeLogDomain findByPositionCode(String positionCode);

    List<PositionChangeLogDomain> findByPositionCodeAndEffectTime(String positionCode, Date effectTime);

    PositionChangeLogDomain findLastedByPositionCodeAndEffectiveTime(String storeCode, Long effectiveTime);

    List<PositionChangeLogDomain> findByStoreCode(String storeCode);
}