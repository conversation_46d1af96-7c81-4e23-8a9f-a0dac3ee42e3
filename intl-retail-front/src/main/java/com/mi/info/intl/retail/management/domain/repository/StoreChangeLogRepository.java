package com.mi.info.intl.retail.management.domain.repository;

import com.mi.info.intl.retail.management.domain.StoreChangeLogDomain;

import java.util.Date;
import java.util.List;

/**
 * 门店变更日志仓储接口
 */
public interface StoreChangeLogRepository {
    /**
     * 保存门店变更日志
     */
    boolean save(StoreChangeLogDomain storeChangeLogDomain);

    /**
     * 根据ID获取门店变更日志
     */
    StoreChangeLogDomain getById(Integer id);

    /**
     * 查询所有门店变更日志
     */
    List<StoreChangeLogDomain> listAll();

    List<StoreChangeLogDomain> findByStoreCodeAndEffectiveTime(String storeCode, Date effectiveTime);

    StoreChangeLogDomain findLastedByStoreCodeAndEffectiveTime(String storeCode, Long effectiveTime);
}
