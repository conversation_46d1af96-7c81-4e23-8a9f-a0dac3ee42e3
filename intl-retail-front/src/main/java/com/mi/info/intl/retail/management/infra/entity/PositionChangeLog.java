package com.mi.info.intl.retail.management.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 岗位变更日志表
 *
 * @TableName position_change_log
 */
@TableName(value = "position_change_log")
@Data
public class PositionChangeLog implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String positionName;
    private String positionCode;
    private String storeCode;
    private String rmsPositionCode;
    private Long effectiveTime;
    private String positionClass;
    private Integer workableType;
    private String positionType;
    private Integer hasPc;
    private Integer hasSr;
    private String positionStatus;
    private String changeType;
    private String changeReason;
    private Date logCreateTime;
    private Integer effectiveStatus;
} 