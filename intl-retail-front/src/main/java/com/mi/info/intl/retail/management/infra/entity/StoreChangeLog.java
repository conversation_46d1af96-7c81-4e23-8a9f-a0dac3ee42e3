package com.mi.info.intl.retail.management.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 门店变更日志表
 *
 * @TableName store_change_log
 */
@TableName(value = "store_change_log")
@Data
public class StoreChangeLog implements Serializable {
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    private String storeName;
    private String storeCode;
    private Integer positionChangeId;
    private String rmsStoreCode;
    private Long effectiveTime;
    private String storeClass;
    private Integer workableType;
    private Integer hasPc;
    private Integer hasSr;
    private String storeStatus;
    private String changeReason;
    private Date recordCreateTime;
    private String storeGrade;
    private String storeType;
    private String storeChannelType;
    private Integer hasFront;
    private Integer hasPos;
    private Integer remainingXiaomiStore;
    private Integer effectiveStatus;
}
