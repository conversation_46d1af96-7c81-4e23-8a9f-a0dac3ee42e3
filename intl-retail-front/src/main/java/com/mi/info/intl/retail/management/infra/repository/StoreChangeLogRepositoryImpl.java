package com.mi.info.intl.retail.management.infra.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mi.info.intl.retail.management.domain.StoreChangeLogDomain;
import com.mi.info.intl.retail.management.domain.repository.StoreChangeLogRepository;
import com.mi.info.intl.retail.management.infra.entity.StoreChangeLog;
import com.mi.info.intl.retail.management.infra.mapper.StoreChangeLogMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 门店变更日志仓储实现
 */
@Repository
public class StoreChangeLogRepositoryImpl implements StoreChangeLogRepository {

    @Autowired
    private StoreChangeLogMapper storeChangeLogMapper;

    @Override
    public boolean save(StoreChangeLogDomain domain) {
        StoreChangeLog entity = convertToEntity(domain);
        return storeChangeLogMapper.insert(entity) > 0;
    }

    @Override
    public StoreChangeLogDomain getById(Integer id) {
        StoreChangeLog entity = storeChangeLogMapper.selectById(id);
        return entity != null ? convertToDomain(entity) : null;
    }

    @Override
    public List<StoreChangeLogDomain> listAll() {
        List<StoreChangeLog> entities = storeChangeLogMapper.selectList(new LambdaQueryWrapper<>());
        return entities.stream().map(this::convertToDomain).collect(Collectors.toList());
    }

    @Override
    public List<StoreChangeLogDomain> findByStoreCodeAndEffectiveTime(String storeCode, Date effectiveTime) {
        LambdaQueryWrapper<StoreChangeLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StoreChangeLog::getStoreCode, storeCode)
                    .eq(StoreChangeLog::getEffectiveStatus, 1)
                    .ge(StoreChangeLog::getEffectiveTime, effectiveTime)
                    .orderByDesc(StoreChangeLog::getEffectiveTime);
        List<StoreChangeLog> entities = storeChangeLogMapper.selectList(queryWrapper);
        return entities.stream().map(this::convertToDomain).collect(Collectors.toList());
    }

    @Override
    public StoreChangeLogDomain findLastedByStoreCodeAndEffectiveTime(String storeCode, Long effectiveTime) {
        LambdaQueryWrapper<StoreChangeLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StoreChangeLog::getStoreCode, storeCode)
                    .eq(StoreChangeLog::getEffectiveStatus, 1)
                    .le(StoreChangeLog::getEffectiveTime, effectiveTime)
                    .orderByDesc(StoreChangeLog::getEffectiveTime);
        StoreChangeLog entity = storeChangeLogMapper.selectOne(queryWrapper);
        return convertToDomain(entity);
    }

    @Override
    public List<StoreChangeLogDomain> findAllByStoreCode(String storeCode) {
        LambdaQueryWrapper<StoreChangeLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StoreChangeLog::getStoreCode, storeCode)
                    .eq(StoreChangeLog::getEffectiveStatus, 1)
                    .orderByDesc(StoreChangeLog::getEffectiveTime);
        List<StoreChangeLog> entities = storeChangeLogMapper.selectList(queryWrapper);
        return entities.stream().map(this::convertToDomain).collect(Collectors.toList());
    }

    private StoreChangeLog convertToEntity(StoreChangeLogDomain domain) {
        if (domain == null) return null;
        StoreChangeLog entity = new StoreChangeLog();
        BeanUtils.copyProperties(domain, entity);
        return entity;
    }

    private StoreChangeLogDomain convertToDomain(StoreChangeLog entity) {
        if (entity == null) return null;
        StoreChangeLogDomain domain = new StoreChangeLogDomain();
        BeanUtils.copyProperties(entity, domain);
        return domain;
    }
}
