package com.mi.info.intl.retail.management.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.mi.info.intl.retail.api.front.position.PositionChangeLogApiService;
import com.mi.info.intl.retail.api.front.position.dto.PositionChangeLogDto;
import com.mi.info.intl.retail.management.domain.PositionChangeLogDomain;
import com.mi.info.intl.retail.management.domain.repository.PositionChangeLogRepository;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class PositionChangeLogServiceImpl implements PositionChangeLogApiService {

    @Resource
    PositionChangeLogRepository positionChangeLogRepository;

    @Override
    public PositionChangeLogDto findLatestByCodeAndTimeStamp(String positionCode, Long effectTime) {

        PositionChangeLogDomain positionChangeLogDomain = positionChangeLogRepository.findLastedByPositionCodeAndEffectiveTime(positionCode, effectTime);
        if (positionChangeLogDomain != null) {
            PositionChangeLogDto positionChangeLogDto = new PositionChangeLogDto();
            BeanUtil.copyProperties(positionChangeLogDomain, positionChangeLogDto);
            return positionChangeLogDto;
        }

        return null;
    }
}
