package com.mi.info.intl.retail.management.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.mi.info.intl.retail.api.front.store.StoreChangeLogApiService;
import com.mi.info.intl.retail.api.front.store.dto.StoreChangeLogDto;
import com.mi.info.intl.retail.management.domain.StoreChangeLogDomain;
import com.mi.info.intl.retail.management.domain.repository.StoreChangeLogRepository;

import javax.annotation.Resource;

public class StoreChangeLogServiceImpl implements StoreChangeLogApiService {

    @Resource
    StoreChangeLogRepository storeChangeLogRepository;

    @Override
    public StoreChangeLogDto findLatestByCodeAndTimeStamp(String positionCode, Long effectTime) {
        StoreChangeLogDomain storeChangeLogDomain = storeChangeLogRepository.findLastedByStoreCodeAndEffectiveTime(positionCode, effectTime);
        // StoreChangeLogDomain转换为StoreChangeLogDto
        if (storeChangeLogDomain != null) {
            StoreChangeLogDto storeChangeLogDto = new StoreChangeLogDto();
            BeanUtil.copyProperties(storeChangeLogDomain, storeChangeLogDto);
            return storeChangeLogDto;
        }
        return null;
    }
}
