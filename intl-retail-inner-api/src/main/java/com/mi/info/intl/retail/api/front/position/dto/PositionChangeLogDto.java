package com.mi.info.intl.retail.api.front.position.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class PositionChangeLogDto implements Serializable {
    private Integer id;
    private String positionName;
    private String positionCode;
    private String storeCode;
    private String rmsPositionCode;
    private Long effectiveTime;
    private String positionClass;
    private Integer workableType;
    private String positionType;
    private Integer hasPc;
    private Integer hasSr;
    private String positionStatus;
    private String changeType;
    private String changeReason;
    private Date logCreateTime;
    private Integer effectiveStatus;
}
