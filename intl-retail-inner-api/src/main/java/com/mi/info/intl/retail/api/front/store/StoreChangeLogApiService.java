package com.mi.info.intl.retail.api.front.store;

import com.mi.info.intl.retail.api.front.position.dto.PositionChangeLogDto;
import com.mi.info.intl.retail.api.front.store.dto.StoreChangeLogDto;

import java.util.List;

public interface StoreChangeLogApiService {

    StoreChangeLogDto findLatestByCodeAndTimeStamp(String storeCode, Long effectTime);

    /**
     * 根据门店code查询所有有效的变更日志
     */
    List<StoreChangeLogDto> findAllByStoreCode(String storeCode);
}
