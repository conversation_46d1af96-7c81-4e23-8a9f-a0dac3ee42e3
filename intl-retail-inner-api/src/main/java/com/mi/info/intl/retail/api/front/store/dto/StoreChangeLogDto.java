package com.mi.info.intl.retail.api.front.store.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class StoreChangeLogDto implements Serializable {
    private Integer id;
    private String storeName;
    private String storeCode;
    private Integer positionChangeId;
    private String rmsStoreCode;
    private Long effectiveTime;
    private Integer storeClass;
    private Integer workableType;
    private Integer hasPc;
    private Integer hasSr;
    private String storeStatus;
    private String changeReason;
    private Date recordCreateTime;
    private Integer storeGrade;
    private Integer storeType;
    private Integer storeChannelType;
    private Integer hasFront;
    private Integer hasPos;
    private Integer remainingXiaomiStore;
    private Integer effectiveStatus;
}
