package com.mi.info.intl.retail.api.product.dto;

import lombok.*;

/**
 * 产品信息DTO
 *
 * <AUTHOR>
 * @date 2025/7/29
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ProductInfoDTO {
    private Integer id;
    private String goodsId;
    private String name;
    private String productLineEn;
    private Long productLineCode;
    private String skuName;
    private Integer isSn;
    private String spuNameEn;
    private String spuName;
    private String code69;
    private String spuId;
    private String shortName;
}
