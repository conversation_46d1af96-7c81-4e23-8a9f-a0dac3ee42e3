package com.mi.info.intl.retail.api.so.rule;

import com.mi.info.intl.retail.api.so.rule.service.IntlRuleRetailerApiService;
import com.mi.info.intl.retail.enums.SoRuleType;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class RuleServiceFactory {

    private final Map<String, IntlRuleRetailerApiService> serviceMap;

    // 构造器注入所有实现类
    public RuleServiceFactory(Map<String, IntlRuleRetailerApiService> serviceMap) {
        this.serviceMap = serviceMap;
    }

    // 根据请求参数中的type，获取对应的实现类
    public IntlRuleRetailerApiService getService(String type) {
        // 遍历所有实现类，匹配type（也可维护一个type->beanName的映射表优化性能）
        for (IntlRuleRetailerApiService service : serviceMap.values()) {
            if (service.getType().equalsIgnoreCase(type)) {
                return service;
            }
        }
        // 未找到匹配的实现类，可抛异常或返回默认实现
        throw new IllegalArgumentException("No subClass found for type [" + type + "]");
    }

    public String getType(SoRuleType soRuleType) {
        if (null == soRuleType) {
            throw new IllegalArgumentException("soRuleType is null");
        }
        switch (soRuleType) {
            case SO_RULE:
                return "soRuleService";
            case LDU_ROLE:
                return "lduRuleService";
            default:
                throw new IllegalArgumentException("Illegal param: soRuleType");
        }
    }
}