package com.mi.info.intl.retail.so.app;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.info.intl.retail.advice.excel.export.ExportExcelAspect;
import com.mi.info.intl.retail.api.country.dto.CountryDTO;
import com.mi.info.intl.retail.api.country.CountryTimeZoneApiService;
import com.mi.info.intl.retail.api.fieldforce.user.UserApiService;
import com.mi.info.intl.retail.api.fieldforce.user.dto.UserInfoDTO;
import com.mi.info.intl.retail.api.rms.RmsStoreTokenApiService;
import com.mi.info.intl.retail.component.ComponentLocator;
import com.mi.info.intl.retail.core.org.service.OrganizePlatformService;
import com.mi.info.intl.retail.core.utils.EasyExcelUtil;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.exception.ErrorCodes;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.MasterDataService;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.AddPlainTextImeiUserRequest;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.BatchDisableRequest;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.DeletePlainTextUserRequest;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.ExcelTemplateRequest;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.ImportBlacklistRequest;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.ImportSnBlacklistErrorDTO;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.PlainTextImeiUserDTO;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.PlainTextUserRequest;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.SnBlackDTO;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.SnBlackRequest;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.UserDTO;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.UserInfoRequest;
import com.mi.info.intl.retail.intlretail.util.AppProviderUtil;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.model.UserInfo;
import com.mi.info.intl.retail.so.app.rpc.FileApiService;
import com.mi.info.intl.retail.so.domain.upload.config.ImeiVerifyConfig;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlImportLog;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlImportTemplate;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoPlaintextImeiUser;
import com.mi.info.intl.retail.so.domain.upload.enums.IntlImportLogTypeEnum;
import com.mi.info.intl.retail.so.domain.upload.enums.IntlSoPlaintextImeiUserStatusEnum;
import com.mi.info.intl.retail.so.domain.upload.service.IntlImportLogService;
import com.mi.info.intl.retail.so.domain.upload.service.IntlImportTemplateService;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoPlaintextImeiUserService;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoSnBlacklistService;
import com.mi.info.intl.retail.so.listener.ImportBatchDisableListener;
import com.mi.info.intl.retail.so.listener.ImportSnBlackListener;
import com.mi.info.intl.retail.utils.UserInfoUtil;
import com.xiaomi.keycenter.okhttp3.OkHttpClient;
import com.xiaomi.keycenter.okhttp3.Request;
import com.xiaomi.keycenter.okhttp3.Response;
import com.xiaomi.nr.eiam.api.dto.userinfo.GetUserInfoResp;
import com.xiaomi.nr.eiam.api.dto.userinfo.Position;
import com.xiaomi.nr.eiam.common.enums.SceneEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.mi.info.intl.retail.exception.ErrorCodes.BIZ_ERROR;
import static com.mi.info.intl.retail.exception.ErrorCodes.SYS_ERROR;

/**
 * 主数据服务实现类
 *
 * <AUTHOR>
 * @date 2025/7/25
 */
@Slf4j
@DubboService(group = "${center.dubbo.group:}", interfaceClass = MasterDataService.class)
@Service
public class MasterDataServiceImpl implements MasterDataService {

    @Resource
    private IntlSoSnBlacklistService intlSoSnBlacklistService;

    @Resource
    private FdsService fdsService;

    @Resource
    private ImeiVerifyConfig imeiVerifyConfig;

    @Resource
    private EasyExcelUtil easyExcelUtil;

    @Resource
    private CountryTimeZoneApiService countryTimeZoneApiService;

    @Resource
    private IntlSoPlaintextImeiUserService intlSoPlaintextImeiUserService;

    @Resource
    private RmsStoreTokenApiService rmsStoreTokenApiService;

    @Resource
    private TransactionTemplate transactionTemplate;

    @Resource
    private FileApiService fileApiService;

    @Resource
    private UserApiService userApiService;

    @Resource
    private IntlImportTemplateService intlImportTemplateService;

    @Resource
    private OrganizePlatformService organizePlatformService;

    @Resource
    private IntlImportLogService importLogService;

    /**
     * 查询sn黑名单
     * @param request
     */
    @Override
    public CommonApiResponse<IPage<SnBlackDTO>> pageQuerySnBlackList(SnBlackRequest request) {
        request.setLocale(UserInfoUtil.getUserContext().getAreaId());
        log.info("查询sn黑名单,{}", request);
       return  new CommonApiResponse<>(intlSoSnBlacklistService.pageList(request));
    }


    /**
     * 导入黑名单
     * @param request
     * @return 异常数据Excel文件URL，如果没有异常数据则返回空字符串
     */
    @Override
    public CommonApiResponse<ImportSnBlacklistErrorDTO> importSnBlackList(ImportBlacklistRequest request) {
          return AppProviderUtil.wrap(log, "MasterDataService::importSnBlackList",
                  request, this::importBlackList, false);
    }


    @Override
    public CommonApiResponse<String> exportSnBlackList(SnBlackRequest request) {
        return AppProviderUtil.wrap(log, "MasterDataService::exportSnBlackList",
                request, this::exportSnSoBlackList, false);
    }




    /**
     * 批量停用
     *
     * @param request
     */
    @Override
    public  CommonApiResponse<String> batchDisable(BatchDisableRequest request) {
       return AppProviderUtil.wrap(log, "MasterDataService::batchDisable",
                request, this::batchDisablePlainTextUser, false);
    }


    /**
     * 分页查询明文IMEI用户
     *
     * @param request 请求参数
     * @return 查询结果
     *
     */
    @Override
    public CommonApiResponse<IPage<PlainTextImeiUserDTO>> pageQueryPlainTextUser(PlainTextUserRequest  request) {
        request.setLocale(UserInfoUtil.getUserContext().getAreaId());
        log.info("分页查询明文IMEI用户：{}", request);
        return  new CommonApiResponse<>(intlSoPlaintextImeiUserService.pageList(request));
    }

    /**
     * 添加明文IMEI用户
     *
     * @param request 请求参数
     * @return 添加结果
     */
    @Override
    public CommonApiResponse<String> addPlainTextUser(AddPlainTextImeiUserRequest request) {
      return AppProviderUtil.wrap(log, "MasterDataService::addPlainTextUser",
                request, this::insertPlainTextUser, false);
    }



    /**
     * 删除明文IMEI用户
     *
     * @param request 明文IMEI用户ID
     * @return
     */
    @Override
    public CommonApiResponse<String> deletePlainTextUser(DeletePlainTextUserRequest request) {
        return AppProviderUtil.wrap(log, "MasterDataService::deletePlainTextUser",
                request, this::removePlainTextUser, false);
    }



    @Override
    public CommonApiResponse<List<UserDTO>> getUserInfoByName(String userName) {
        List<UserInfoDTO> userInfoDTOS = userApiService.getUserByName(userName);
        List<UserDTO> userDTOS = ComponentLocator.getConverter().convertList(userInfoDTOS, UserDTO.class);
        return  CommonApiResponse.success(userDTOS);
    }


    @Override
    public CommonApiResponse<String> getBlackListExcelTemplate(ExcelTemplateRequest request) {

        log.info("getBlackListExcelTemplate get request, type={}", request);

        IntlImportTemplate template = intlImportTemplateService.getOne(Wrappers.<IntlImportTemplate>lambdaQuery()
                .eq(IntlImportTemplate::getType, request.getType())
                .eq(IntlImportTemplate::getBusinessType, request.getBusinessType()));

        if (Objects.isNull(template)) {
            log.error("getBlackListExcelTemplate template not found, type={}", request.getType());
            return CommonApiResponse.failure(SYS_ERROR.getCode(), "template not found");
        }

        return CommonApiResponse.success(template.getFileUrl());
    }


    @Override
    public CommonApiResponse<GetUserInfoResp> getUserInfo(UserInfoRequest request) {
       return  AppProviderUtil.wrap(log, "MasterDataService::getUserInfo",
               request.getKeyWord(), organizePlatformService::getUserInfo, false);
    }


    /**
     * 从URL下载文件
     */
    private File getFileFromUrl(String fileUrl) throws IOException {

        log.info("开始获取文件: {}", fileUrl);

        // 使用OkHttp下载文件
        OkHttpClient client = new OkHttpClient();
        Request request = new Request.Builder().url(fileUrl).build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("文件下载失败，HTTP状态码: {}", response.code());
                throw new IOException("文件下载失败，HTTP状态码: " + response.code());
            }

            // 创建临时文件
            File tempFile = File.createTempFile("import_", ".xlsx");
            try (InputStream inputStream = response.body().byteStream();
                 FileOutputStream outputStream = new FileOutputStream(tempFile)) {
                
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }
            
            return tempFile;
        }
    }


    private ImportSnBlacklistErrorDTO importBlackList(ImportBlacklistRequest request) {
        log.info("开始导入黑名单，文件URL: {}", request);

        if (StringUtils.isBlank(request.getFileUrl())) {
            log.error("文件URL不能为空");
            throw new BizException(BIZ_ERROR,  "文件URL不能为空");
        }


        File inputFile = null;
        try {
            UserInfo userContext = UserInfoUtil.getUserContext();
            // 1.读取URL数据
            inputFile = getFileFromUrl(request.getFileUrl());

            // 2.读取dayu配置进行sn的hash并保存数据
            List<String> hashCountryList = imeiVerifyConfig.getHashCountryList();

            ImportSnBlacklistErrorDTO errorDataFileUrl = new ImportSnBlacklistErrorDTO();
            //3. 解析Excel文件
            ImportSnBlackListener importSnBlackListener =
                    new ImportSnBlackListener(intlSoSnBlacklistService,
                            countryTimeZoneApiService,
                            rmsStoreTokenApiService,
                            transactionTemplate,
                            fdsService,
                            hashCountryList, userContext,
                            errorDataFileUrl);

            // 4.读取Excel文件
            easyExcelUtil.readFromStream(Files.newInputStream(inputFile.toPath()),
                    ImportSnBlackListener.SnBlackImportData.class,
                    importSnBlackListener, 1);

            return errorDataFileUrl;

        } catch (Exception e) {
            log.error("导入黑名单失败: {}", e.getMessage(), e);
            throw new BizException(BIZ_ERROR,  e.getMessage());
        } finally {
            // 清理临时文件
            if (inputFile != null && inputFile.exists()) {
                inputFile.delete();
            }
        }
    }

    private String exportSnSoBlackList(SnBlackRequest request) {
    log.info("开始导出黑名单数据，查询条件: {}", request);
    File tempFile = null;
    ExcelWriter excelWriter = null;
    request.setLocale(UserInfoUtil.getUserContext().getAreaId());
    try {
        // 创建临时文件用于存储导出的Excel数据
        tempFile = File.createTempFile("snBlacklist", ".xlsx");

        // 初始化Excel写入器，设置导出类为SnBlackDTO，并注册自适应列宽样式策略
        excelWriter = EasyExcel.write(tempFile, SnBlackDTO.class)
                .registerWriteHandler(new ExportExcelAspect.AdaptiveWidthStyleStrategy()).build();
        // 创建写入Sheet页，命名为"任务列表"
        WriteSheet writeSheet = EasyExcel.writerSheet("任务列表").build();

        // 设置分页参数：每页1000条记录
        long pageSize = 1000L;
        long currentPage = 1L;
        boolean hasNext = true;
        long total = 0L;

        // 异步上传Future，用于后续异步上传文件到FDS
        CompletableFuture<String> uploadFuture = null;
        // 生成时间戳作为文件名的一部分，确保文件名唯一性
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000L);
        String fileName = "snBlackList" + timestamp + ".xlsx";

        // 分页查询并写入数据到Excel文件
        while (hasNext) {
            // 设置当前页码和页面大小
            request.setPageNum(currentPage);
            request.setPageSize(pageSize);

            // 调用分页查询接口获取当前页数据
            CommonApiResponse<IPage<SnBlackDTO>> iPageCommonApiResponse = this.pageQuerySnBlackList(request);
            IPage<SnBlackDTO> pageData = iPageCommonApiResponse.getData();

            // 在第一页查询时获取总记录数，用于计算分页信息
            if (currentPage == 1) {
                total = pageData.getTotal();
            }

            // 获取当前页的记录列表
            List<SnBlackDTO> records = pageData.getRecords();
            // 如果当前页没有数据，则结束循环
            if (CollUtil.isEmpty(records)) {
                hasNext = false;
                continue;
            }

            // 将当前页数据写入Excel文件
            excelWriter.write(records, writeSheet);

            // 判断是否还有下一页数据
            hasNext = currentPage * pageSize < total;
            currentPage++;
        }

        // 完成Excel写入操作
        if (excelWriter != null) {
            excelWriter.finish();
        }

        // 使用CompletableFuture异步上传文件到FDS，避免阻塞主线程
        File finalTempFile = tempFile;
        uploadFuture = CompletableFuture.supplyAsync(() -> {
            try {
                // 调用FDS服务上传文件并返回访问URL
                return fdsService.upload(fileName, finalTempFile, true).getUrl();
            } catch (Exception e) {
                log.error("文件上传失败", e);
                throw new RuntimeException("文件上传失败", e);
            }
        });

        // 等待异步上传完成并返回文件访问URL
        return uploadFuture.get();

    } catch (InterruptedException e) {
        // 重新中断当前线程
        Thread.currentThread().interrupt();
        log.error("导出SN黑名单被中断, request: {}", request, e);
        throw new BizException(BIZ_ERROR, "导出被中断");
    } catch (Exception e) {
        log.error("导出SN黑名单异常, request: {}", request, e);
        throw new BizException(BIZ_ERROR, e.getMessage());
    } finally {
        // 无论是否发生异常，都要确保资源正确释放
        if (excelWriter != null) {
            excelWriter.finish();
        }
        // 删除临时文件，释放磁盘空间
        if (tempFile != null && tempFile.exists()) {
            tempFile.delete();
        }
    }
}




    /**
     *  删除明文IMEI用户
     * @param request
     * @return
     */
    private String removePlainTextUser(DeletePlainTextUserRequest request) {
        log.info("开始删除明文IMEI用户: {}", request.getId());

        IntlSoPlaintextImeiUser intlSoPlaintextImeiUser =
                intlSoPlaintextImeiUserService.getOne(Wrappers.<IntlSoPlaintextImeiUser>lambdaQuery()
                        .eq(IntlSoPlaintextImeiUser::getId, request.getId())
                        .eq(IntlSoPlaintextImeiUser::getStatus, IntlSoPlaintextImeiUserStatusEnum.ENABLED.getCode()));

        if (intlSoPlaintextImeiUser == null) {
            throw new BizException(BIZ_ERROR, "Plain Text Imei User Not Exists.");
        }


        intlSoPlaintextImeiUser.setStatus(IntlSoPlaintextImeiUserStatusEnum.DISABLED.getCode());
        intlSoPlaintextImeiUserService.updateById(intlSoPlaintextImeiUser);
        return "success";
    }

    /**
     * 添加明文IMEI用户
     *
     * @param request
     * @return
     */
    private String insertPlainTextUser(AddPlainTextImeiUserRequest request) {
        log.info("开始添加明文IMEI用户: {}", request);

        // 参数校验
        validateRequest(request);

        // 获取当前用户信息
        UserInfo userInfo = UserInfoUtil.getUserContext();
        GetUserInfoResp getUserInfoResp = organizePlatformService.getUserInfo(request.getMiId());
        log.info("获取用户职位信息: {}", getUserInfoResp);

        if (null == getUserInfoResp) {
            throw new BizException(BIZ_ERROR, "获取用户职位信息失败");
        }



        // 创建明文IMEI用户实体
        IntlSoPlaintextImeiUser intlSoPlaintextImeiUser = buildPlainTextImeiUser(
                request, userInfo, getUserInfoResp);

        // 保存用户信息
        intlSoPlaintextImeiUserService.save(intlSoPlaintextImeiUser);

        // 处理文件上传和日志记录
        handleFileUploadAndLogging(request, intlSoPlaintextImeiUser);

        log.info("添加明文IMEI用户成功: {}", intlSoPlaintextImeiUser);
        return "success";
    }


    /**
     * 批量停用明文IMEI用户
     *
     * @param request
     * @return
     */
    private String batchDisablePlainTextUser(BatchDisableRequest request) {
        log.info("批量停用开始,入参:{}", request);
        File inputFile = null;
        try {
            UserInfo userContext = UserInfoUtil.getUserContext();

            log.info("批量停用开始,获取用户信息:{}", userContext);

            // 1.读取URL数据
            inputFile = getFileFromUrl(request.getFileUrl());

            //2. 解析Excel文件
            List<ImportBatchDisableListener.BatchDisableImportData> dataList = new ArrayList<>();

            ImportBatchDisableListener importBatchDisableListener =
                    new ImportBatchDisableListener(intlSoSnBlacklistService,
                            countryTimeZoneApiService,
                            rmsStoreTokenApiService,
                            transactionTemplate,
                            dataList, userContext);

            // 3.读取Excel文件
            easyExcelUtil.readFromStream(Files.newInputStream(inputFile.toPath()),
                    ImportBatchDisableListener.BatchDisableImportData.class,
                    importBatchDisableListener, 1);
            return  "success";
        } catch (Exception e) {
            log.error("批量停用失败: {}", e.getMessage(), e);
            throw new BizException(ErrorCodes.EXPORT_TO_EXCEL_FAILED, e.getMessage());
        } finally {
            // 清理临时文件
            if (inputFile != null && inputFile.exists()) {
                inputFile.delete();
            }
        }
    }

    private void validateRequest(AddPlainTextImeiUserRequest request) {

        boolean isMoreThanThree = null != request.getFileDataList()
                && request.getFileDataList().size() > 3;

        if (isMoreThanThree) {
            throw new BizException("最多只能添加3张图片");
        }
    }

    /**
     * 构建国家名称字符串
     *
     * @param countryCodeSet 国家代码集合
     * @return 国家名称字符串
     */
    private String buildCountryNames(Set<String> countryCodeSet) {
        return countryCodeSet.stream()
                .map(code -> countryTimeZoneApiService.getCountryInfoByCode(code)
                        .map(CountryDTO::getCountryName)
                        .orElse(StringUtils.EMPTY))
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.joining(","));
    }

    /**
     * 构建明文IMEI用户实体
     *
     * @param request       请求参数
     * @param userInfo      用户信息
     * @param getUserInfoResp  组织中台用户信息
     * @return 明文IMEI用户实体
     */
    private IntlSoPlaintextImeiUser buildPlainTextImeiUser(AddPlainTextImeiUserRequest request,
                                                           UserInfo userInfo,
                                                           GetUserInfoResp getUserInfoResp) {
        IntlSoPlaintextImeiUser user = new IntlSoPlaintextImeiUser();
        // 提取用户职位信息
        Set<String> countryCodeSet = getUserInfoResp.getPositionList().stream()
                .filter(Objects::nonNull)
                .map(Position::getAreaId)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toSet());

        Set<Integer> jobTitleIdSet = getUserInfoResp.getPositionList().stream()
                .map(Position::getPositionId)
                .collect(Collectors.toSet());

        // 构建国家名称和代码字符串
        String countryNames = buildCountryNames(countryCodeSet);
        String countryCodes = String.join(",", countryCodeSet);

        user.setCountryName(countryNames);
        user.setCountryCode(countryCodes);
        user.setUserMid(request.getMiId());

        // 处理文件ID
        String photoIds = Optional.ofNullable(request.getFileDataList())
                .filter(list -> !list.isEmpty())
                .map(list -> list.stream()
                        .map(fileData -> String.valueOf(fileData.getFileId()))
                        .collect(Collectors.joining(",")))
                .orElse("");
        user.setPhotoIds(photoIds);

        // 处理职位ID
        String jobTitleIds = jobTitleIdSet.isEmpty() ? "" :
                jobTitleIdSet.stream()
                        .map(String::valueOf)
                        .collect(Collectors.joining(","));
        user.setJobTitleId(jobTitleIds);
        user.setUserName(getUserInfoResp.getName());

        // 设置创建和修改信息
        long currentTime = System.currentTimeMillis();
        user.setCreatedBy(userInfo.getMiID());
        user.setCreatedOn(currentTime);
        user.setModifiedBy(userInfo.getMiID());
        user.setModifiedOn(currentTime);

        return user;
    }

    /**
     * 处理文件上传和日志记录
     *
     * @param request 请求参数
     * @param user    明文IMEI用户实体
     */
    private void handleFileUploadAndLogging(AddPlainTextImeiUserRequest request,
                                            IntlSoPlaintextImeiUser user) {
        // 解析照片ID列表
        List<Long> photoIdList = parsePhotoIdList(user.getPhotoIds());

        if (CollUtil.isEmpty(photoIdList)) {
            return;
        }

        // 二次提交文件
        fileApiService.commit(photoIdList);

        // 创建并保存导入日志
        List<IntlImportLog> importLogList = createImportLogs(request, photoIdList);
        importLogService.saveBatch(importLogList);
    }

    /**
     * 解析照片ID列表
     *
     * @param photoIds 照片ID字符串
     * @return 照片ID列表
     */
    private List<Long> parsePhotoIdList(String photoIds) {
        return Optional.ofNullable(photoIds)
                .filter(StringUtils::isNotEmpty)
                .map(ids -> Arrays.stream(ids.split(","))
                        .map(String::trim)
                        .filter(StringUtils::isNotEmpty)
                        .map(Long::parseLong)
                        .collect(Collectors.toList()))
                .orElse(new ArrayList<>());
    }

    /**
     * 创建导入日志列表
     *
     * @param request     请求参数
     * @param photoIdList 照片ID列表
     * @return 导入日志列表
     */
    private List<IntlImportLog> createImportLogs(AddPlainTextImeiUserRequest request,
                                                 List<Long> photoIdList) {
        List<IntlImportLog> importLogList = new ArrayList<>();
        long currentTime = System.currentTimeMillis();

        for (AddPlainTextImeiUserRequest.FileData fileData : request.getFileDataList()) {
            IntlImportLog importLog = new IntlImportLog();
            importLog.setType(IntlImportLogTypeEnum.PLAIN_TEXT_USER.getCode());
            importLog.setActionType(2);
            importLog.setSourceFileId(fileData.getFileId());
            importLog.setSourceFileUrl(fileData.getUrl());
            importLog.setCreatedBy(request.getMiId());
            importLog.setStatus(0);
            importLog.setCreatedAt(currentTime);
            importLog.setUpdatedAt(currentTime);
            importLogList.add(importLog);
        }

        return importLogList;
    }
}
