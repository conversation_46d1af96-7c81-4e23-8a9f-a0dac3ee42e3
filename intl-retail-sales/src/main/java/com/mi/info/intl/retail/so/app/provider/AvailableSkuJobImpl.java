package com.mi.info.intl.retail.so.app.provider;

import com.mi.info.intl.retail.intlretail.service.api.job.AvailableSkuJob;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSkuAvailableService;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;

@ApiModule(value = "AvailableSkuJob", apiInterface = AvailableSkuJob.class)
@DubboService(group = "${retail.dubbo.group:}", interfaceClass = AvailableSkuJob.class)
public class AvailableSkuJobImpl implements AvailableSkuJob {

    @Resource
    private IntlSkuAvailableService intlSkuAvailableService;
    @Override
    public void initSalestime() {
        intlSkuAvailableService.initZeroSalesTimeData();
    }
}
