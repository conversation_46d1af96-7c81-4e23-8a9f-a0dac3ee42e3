package com.mi.info.intl.retail.so.app.provider.upload;

import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.google.zxing.*;
import com.mi.info.intl.retail.api.fieldforce.user.UserApiService;
import com.mi.info.intl.retail.api.fieldforce.user.dto.UserInfoDTO;
import com.mi.info.intl.retail.api.file.FileUploadApiService;
import com.mi.info.intl.retail.api.file.dto.PhotoDataInfoDTO;
import com.mi.info.intl.retail.api.front.position.IntlPositionApiService;
import com.mi.info.intl.retail.api.front.position.dto.PositionStoreInfoDTO;
import com.mi.info.intl.retail.api.front.store.RmsStoreService;
import com.mi.info.intl.retail.api.front.store.StoreChangeLogApiService;
import com.mi.info.intl.retail.api.front.store.dto.StoreChangeLogDto;
import com.mi.info.intl.retail.api.product.ProductApiService;
import com.mi.info.intl.retail.api.product.dto.ProductInfoDTO;
import com.mi.info.intl.retail.enums.SerialNumberType;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.*;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.ImeiRuleDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.ImeiUploadService;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.so.app.mq.dto.RetailSyncToRmsInfo;
import com.mi.info.intl.retail.so.app.provider.enums.*;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlRmsRrp;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoImei;
import com.mi.info.intl.retail.so.domain.upload.service.IntlRmsRrpService;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoImeiService;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoOrgInfoService;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoUserInfoService;
import com.mi.info.intl.retail.so.infra.database.dataobject.rule.IntlSoRuleDetail;
import com.mi.info.intl.retail.so.infra.database.mapper.rule.IntlSoRuleDetailMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoImeiMapper;
import com.mi.info.intl.retail.so.util.MaskUtil;
import com.mi.info.intl.retail.so.util.SalesTimeValidUtil;
import com.mi.info.intl.retail.utils.BarcodeScannerUtil;
import com.mi.info.intl.retail.utils.JsonUtil;
import com.mi.info.intl.retail.utils.SnImeiValidateUtil;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import com.xiaomi.mone.dubbo.docs.annotations.ApiDoc;
import com.xiaomi.mone.dubbo.docs.annotations.ApiModule;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

/**
 * IMEI数据提交服务实现
 *
 * <AUTHOR>
 * @date 2025/7/28
 */
@Slf4j
@Service
@DubboService(group = "${center.dubbo.group:}", interfaceClass = ImeiUploadService.class)
@ApiModule(value = "国际新零售平台", apiInterface = ImeiUploadService.class)
public class ImeiUploadServiceImpl implements ImeiUploadService {

    @Resource
    private IntlSoRuleDetailMapper intlSoRuleDetailMapper;

    @Resource
    private IntlSoOrgInfoService intlSoOrgInfoService;

    @Resource
    private IntlSoUserInfoService intlSoUserInfoService;

    @Resource
    private StoreChangeLogApiService storeChangeLogApiService;

    @Resource
    private IntlSoImeiService intlSoImeiService;
    @Resource
    private ProductApiService productApiService;

    @Resource
    private FileUploadApiService fileUploadApiService;

    @Resource
    private IntlPositionApiService intlPositionApiService;

    @Resource
    private UserApiService userApiService;

    @Resource
    private RmsStoreService rmsStoreService;

    @Resource
    private IntlSoImeiMapper intlSoImeiMapper;

    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Resource
    private IntlRmsRrpService intlRmsRrpService;

    @Resource
    ImeiReportVerifyServiceImpl imeiReportVerifyServiceImpl;

    // 定义图片上传模块名称
    private static final String MODULE_NAME = "imei_upload";
    // 定义mq消息常量
    private static final String OPERATION_TYPE = "create";
    private static final String DATA_TYPE = "imei";

    private static final String INTERNAL_ERROR = "Internal error";

    @Value("${intl-retail.rocketmq.to-rms.topic}")
    private String topic;


    // 促销员职位代码
    private static final List<Long> PROMOTER_TITLES = Arrays.asList(500900001L, 100000027L, 100000026L);

    @ApiDoc(description = "IMEI数据提交", value = "/api/so/submitImei")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonApiResponse<Object> submitImei(SubmitImeiReq request) {
        log.info("submitImei start, request: {}", request);

        try {
            // 1. 必填字段校验
            CommonApiResponse<Object> validateResult = validateRequest(request);
            if (validateResult != null) {
                return validateResult;
            }

            // 2. 查询SO上报规则
            IntlSoRuleDetail ruleDetail = queryRuleDetail(request.getRuleId());
            if (ruleDetail == null) {
                return new CommonApiResponse<>(400, "SO上报规则不存在", null);
            }

            // 3. 查询阵地和门店信息
            PositionStoreInfoDTO positionStoreInfo = queryPositionStoreInfo(request.getPositionCode());
            if (positionStoreInfo == null) {
                return new CommonApiResponse<>(400, "阵地或门店信息不存在", null);
            }

            // 4. 查询用户信息
            UserInfoDTO userInfo = queryUserInfo(request.getMiId());
            if (userInfo == null) {
                return new CommonApiResponse<>(400, "用户信息不存在", null);
            }

            // 5. 查询产品信息
            Map<Long, ProductInfoDTO> productMap = queryProductInfo(request.getDetailList());
            if (productMap.isEmpty()) {
                return new CommonApiResponse<>(400, "产品信息不存在", null);
            }

            // 6. 查询产品价格信息
            Map<String, IntlRmsRrp> rrpMap = intlRmsRrpService.queryRrpInfo(productMap.values(), request.getCountryCode());

            // 8. 创建相关数据
            List<IntlSoImei> imeiList = createImeiData(request, ruleDetail, positionStoreInfo, userInfo, productMap, rrpMap);

            // 9. 触发数据同步
            sendMqMessage(imeiList);

            log.info("submitImei success");
            return new CommonApiResponse<>(null);

        } catch (Exception e) {
            log.error("submitImei error", e);
            return new CommonApiResponse<>(500, INTERNAL_ERROR + e.getMessage(), null);
        }
    }

    /**
     * 校验请求参数
     */
    private CommonApiResponse<Object> validateRequest(SubmitImeiReq request) {
        if (request == null) {
            return new CommonApiResponse<>(400, "请求参数不能为空", null);
        }

        if (request.getRuleId() == null) {
            return new CommonApiResponse<>(400, "匹配规则ID不能为空", null);
        }

        if (StringUtils.isBlank(request.getCountryCode())) {
            return new CommonApiResponse<>(400, "国家编码不能为空", null);
        }

        if (StringUtils.isBlank(request.getPositionCode())) {
            return new CommonApiResponse<>(400, "阵地编码不能为空", null);
        }

        if (StringUtils.isBlank(request.getStoreCode())) {
            return new CommonApiResponse<>(400, "门店编码不能为空", null);
        }

        if (StringUtils.isBlank(request.getUserId())) {
            return new CommonApiResponse<>(400, "用户GUID不能为空", null);
        }

        if (request.getMiId() == null) {
            return new CommonApiResponse<>(400, "用户miId不能为空", null);
        }

        if (request.getUserTitle() == null) {
            return new CommonApiResponse<>(400, "职位编码不能为空", null);
        }

        if (CollectionUtils.isEmpty(request.getDetailList())) {
            return new CommonApiResponse<>(400, "IMEI明细不能为空", null);
        }

        // 校验明细字段
        for (ImeiDetailDto detail : request.getDetailList()) {
            if (StringUtils.isBlank(detail.getImei())) {
                return new CommonApiResponse<>(400, "IMEI不能为空", null);
            }
            if (StringUtils.isBlank(detail.getSn())) {
                return new CommonApiResponse<>(400, "序列号不能为空", null);
            }
            if (detail.getProductId() == null) {
                return new CommonApiResponse<>(400, "产品ID不能为空", null);
            }
            if (detail.getSalesTime() == null) {
                return new CommonApiResponse<>(400, "销售时间不能为空", null);
            }
        }

        return null;
    }

    /**
     * 查询SO上报规则
     */
    private IntlSoRuleDetail queryRuleDetail(Long ruleId) {
        try {
            return intlSoRuleDetailMapper.selectById(ruleId);
        } catch (Exception e) {
            log.error("查询SO上报规则失败, ruleId: {}", ruleId, e);
            return null;
        }
    }

    /**
     * 查询阵地和门店信息
     */
    private PositionStoreInfoDTO queryPositionStoreInfo(String positionCode) {
        try {
            Optional<PositionStoreInfoDTO> result = intlPositionApiService.queryPositionWithStoreByCode(positionCode);

            return result.orElse(null);
        } catch (Exception e) {
            log.error("查询阵地门店信息失败, positionCode: {}", positionCode, e);
            return null;
        }
    }

    /**
     * 查询用户信息
     */
    private UserInfoDTO queryUserInfo(Long miId) {
        try {
            Optional<UserInfoDTO> result = userApiService.queryUserByMiId(miId);
            return result.orElse(null);
        } catch (Exception e) {
            log.error("查询用户信息失败, miId: {}", miId, e);
            return null;
        }
    }

    /**
     * 查询产品信息
     */
    private Map<Long, ProductInfoDTO> queryProductInfo(List<ImeiDetailDto> detailList) {
        try {
            List<String> productCodes = detailList.stream()
                    .map(ImeiDetailDto::getProductCode)
                    .distinct()
                    .collect(Collectors.toList());

            return productApiService.queryProductsByGoodIds(productCodes);
        } catch (Exception e) {
            log.error("查询产品信息失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 创建IMEI相关数据
     */
    private List<IntlSoImei> createImeiData(SubmitImeiReq request, IntlSoRuleDetail ruleDetail,
                                            PositionStoreInfoDTO positionStoreInfo, UserInfoDTO userInfo,
                                            Map<Long, ProductInfoDTO> productMap, Map<String, IntlRmsRrp> rrpMap) {

        // 1. 创建用户信息数据
        Long userInfoId = intlSoUserInfoService.createUserInfo(userInfo, request.getMiId());

        // 2. 创建IMEI明细数据
        List<IntlSoImei> imeiList = createImeiDetails(request, ruleDetail, userInfoId,
                productMap, rrpMap, positionStoreInfo);

        // 3. 设置isPhotoExist字段
        setPhotoExistFlag(request, imeiList);

        // 4. 批量插入IMEI数据
        if (CollectionUtils.isNotEmpty(imeiList)) {
            // 使用Service的saveBatch方法，这样可以获取到生成的ID
            intlSoImeiService.saveBatch(imeiList);
        }

        // 5. 创建图片数据
        createPhotoData(request, imeiList, userInfo);

        return imeiList;
    }

    /**
     * 创建IMEI明细数据（支持门店变更日志）
     */
    private List<IntlSoImei> createImeiDetails(SubmitImeiReq request, IntlSoRuleDetail ruleDetail,
                                                           Long userInfoId, Map<Long, ProductInfoDTO> productMap,
                                                           Map<String, IntlRmsRrp> rrpMap, PositionStoreInfoDTO positionStoreInfo) {

        List<IntlSoImei> imeiList = new ArrayList<>();
        Long currentTime = SalesTimeValidUtil.getCurrentTimestamp();

        // 一次性查询该门店的所有变更日志
        List<StoreChangeLogDto> allStoreLogs = storeChangeLogApiService.findAllByStoreCode(request.getStoreCode());

        for (ImeiDetailDto detail : request.getDetailList()) {
            ProductInfoDTO product = productMap.get(detail.getProductId());
            if (product == null) {
                log.warn("产品信息不存在, productId: {}", detail.getProductId());
                continue;
            }
            // 根据上报类型赋值销售时间（APP端上报的赋值当前时间）
            if (detail.getReportingType().equals(ReportingTypeEnum.APP.getValue())) {
                detail.setSalesTime(currentTime);
            }
            // 根据销售时间从已查询的日志中匹配对应的门店信息
            Long orgInfoId = intlSoOrgInfoService.createOrgInfoWithStoreLogs(positionStoreInfo, request.getCountryCode(),
                    allStoreLogs, detail.getSalesTime());

            IntlSoImei imei = new IntlSoImei();

            // 非哈希国家存明文
            if (detail.getIsHashCountry() != 1) {
                imei.setSn(detail.getSn());
                imei.setImei1(detail.getImei());
                imei.setImei2(detail.getImei2());
            }
            // 掩码处理
            imei.setSnMask(MaskUtil.maskString(detail.getSn()));
            imei.setImei1Mask(MaskUtil.maskString(detail.getImei()));
            imei.setImei2Mask(MaskUtil.maskString(detail.getImei2()));

            // 哈希值
            imei.setSnHash(detail.getSnhash());

            // 产品信息
            imei.setImeiFromHub(null);
            imei.setProductCode(product.getGoodsId());
            imei.setProductName(product.getName());
            imei.setProductLineCode(product.getProductLineCode());
            imei.setProductShortName(product.getShortName());

            // 关联信息
            imei.setOrgInfoId(orgInfoId);
            imei.setUserInfoId(userInfoId);
            imei.setImeiRuleId(String.valueOf(ruleDetail.getId()));
            imei.setSalesmanMid(request.getMiId());
            imei.setDetailId(detail.getDetailId()); // 图片关联id
            imei.setSupplierCode(positionStoreInfo.getSupplierCode());
            imei.setIsPhotoExist(PhotoExistEnum.NOT_EXIST.getValue()); // 默认值为0，后续根据是否有图片进行更新

            // 阵地/门店code字段赋值
            imei.setStoreRmsCode(positionStoreInfo.getStoreCode());
            imei.setPositionRmsCode(positionStoreInfo.getPositionCode());

            // IMEI规则解析
            List<ImeiRuleDTO> list = JSON.parseArray(ruleDetail.getImeiRuleList(), ImeiRuleDTO.class);
            // 根据入参中的userTitle和productLineEn匹配IMEI规则（ImeiRuleDTO），获取before和after值
            Optional<ImeiRuleDTO> imeiRule = list.stream()
                    .filter(rule -> rule.getUserTitleList().stream()
                            .anyMatch(userTitleItem -> userTitleItem.getUserTitleId()
                                    .equals(String.valueOf(request.getUserTitle())))
                            && rule.getProductLineList().stream()
                            .anyMatch(productLineItem -> productLineItem.getProductLineId()
                                    .equals(String.valueOf(product.getProductLineCode()))))
                    .findFirst();
            if (imeiRule.isPresent()) {
                imei.setImeiRuleBefore(imeiRule.get().getBefore());
                imei.setImeiRuleAfter(imeiRule.get().getAfter());
                imei.setImeiRuleIsActivingCheck(true);
            }

            // 销售时间
            imei.setSalesTime(detail.getSalesTime());
            
            // 激活校验信息
            imei.setActivationVerificationTime(currentTime);
            imei.setActivationTime(null);
            imei.setActivationFrequency(null);
            imei.setActivationSite(null);

            // 验证状态
            imei.setVerifyingState(VerifyingStateEnum.ABNORMAL.getValue()); // 默认abnormal
            imei.setVerificationResult(VerificationResultEnum.VERIFYING.getValue()); // 默认验证中
            imei.setVerifyResultDetail(null);
            imei.setFailedReason(null);
            imei.setFailedReasonDetail(null);
            imei.setSiVerifyResult(null);

            // 上报类型
            imei.setReportingType(
                    detail.getReportingType() != null ? detail.getReportingType() : ReportingTypeEnum.APP.getValue());

            // 其他字段
            imei.setFirstLevelAccountCode(null);
            imei.setFinalSalesCountry(null);
            imei.setAllowSalesCountry(null);
            imei.setNote(detail.getNote());
            imei.setSalesChannel(detail.getSalesChannel());

            // 价格信息
            IntlRmsRrp rrp = rrpMap.get(String.valueOf(product.getGoodsId()));
            if (rrp != null) {
                imei.setCurrency(rrp.getCurrency());
                imei.setRrpCode(rrp.getRrpCode());
                imei.setRrp(rrp.getRrp());
            }

            // 导入日志id及批次信息(uuid)
            imei.setBatchId(request.getBatchId());
            imei.setBatchIdStr(request.getBatchIdStr());

            // 创建信息
            imei.setCreatedBy(request.getMiId());
            imei.setCreatedOn(currentTime);
            imei.setModifiedBy(request.getMiId());
            imei.setModifiedOn(currentTime);

            // 其他
            imei.setRmsId(null);
            imei.setDataFrom(DataFromEnum.MIRETAIL.getValue()); // 数据来源,默认赋值mi retail

            imeiList.add(imei);
        }

        return imeiList;
    }

    /**
     * IMEI数据同步RMS
     *
     * @param imeiList imei数据列表
     */
    private void sendMqMessage(List<IntlSoImei> imeiList) {
        if (CollectionUtils.isEmpty(imeiList)) {
            return;
        }
        RetailSyncToRmsInfo retailSyncToRmsInfo = new RetailSyncToRmsInfo();
        retailSyncToRmsInfo.setOperateType(OPERATION_TYPE);
        retailSyncToRmsInfo.setDataType(DATA_TYPE);
        // 提取列表中的id，逗号分隔
        retailSyncToRmsInfo.setDataId(imeiList.stream().map(IntlSoImei::getId).map(String::valueOf)
                .collect(Collectors.joining(",")));
        final String reqStr = JsonUtil.bean2json(retailSyncToRmsInfo);

        // 2.发送mq
        try {
            final SendResult sendResult = rocketMQTemplate.syncSend(topic, reqStr);
            log.info("submitImei sendResult msgId:{}", sendResult.getMsgId());
        } catch (Exception e) {
            log.info("submitImei send failed reqStr:{}", reqStr);
        }
    }

    /**
     * 设置IMEI数据的isPhotoExist字段
     */
    private void setPhotoExistFlag(SubmitImeiReq request, List<IntlSoImei> imeiList) {
        if (CollectionUtils.isEmpty(request.getPhotoList()) || CollectionUtils.isEmpty(imeiList)) {
            return;
        }

        // 建立detailId到IMEI对象的映射
        Map<String, IntlSoImei> detailIdToImeiMap = new HashMap<>();
        for (int i = 0; i < request.getDetailList().size() && i < imeiList.size(); i++) {
            String detailId = request.getDetailList().get(i).getDetailId();
            IntlSoImei imei = imeiList.get(i);
            detailIdToImeiMap.put(detailId, imei);
        }

        // 根据图片列表设置isPhotoExist字段（需要检查URL不为空）
        for (PhotoDto photo : request.getPhotoList()) {
            // 只有当图片的URL不为空时才设置为1
            if (StringUtils.isNotBlank(photo.getUrl())) {
                IntlSoImei imei = detailIdToImeiMap.get(photo.getDetailId());
                if (imei != null) {
                    imei.setIsPhotoExist(PhotoExistEnum.EXIST.getValue()); // 有图片且URL不为空则设置为1
                }
            }
        }
    }

    /**
     * 创建图片数据
     */
    private void createPhotoData(SubmitImeiReq request, List<IntlSoImei> imeiList, UserInfoDTO userInfo) {
        if (CollectionUtils.isEmpty(request.getPhotoList()) || CollectionUtils.isEmpty(imeiList)) {
            return;
        }

        // 建立detailId到imeiId的映射
        Map<String, Long> detailIdToImeiIdMap = new HashMap<>();
        for (int i = 0; i < request.getDetailList().size() && i < imeiList.size(); i++) {
            String detailId = request.getDetailList().get(i).getDetailId();
            Long imeiId = imeiList.get(i).getId();
            detailIdToImeiIdMap.put(detailId, imeiId);
        }

        List<PhotoDataInfoDTO> photoDataList = new ArrayList<>();
        Long currentTime = SalesTimeValidUtil.getCurrentTimestamp();

        for (PhotoDto photo : request.getPhotoList()) {
            Long relatedId = detailIdToImeiIdMap.get(photo.getDetailId());
            if (relatedId == null) {
                log.warn("找不到对应的IMEI记录, detailId: {}", photo.getDetailId());
                continue;
            }

            PhotoDataInfoDTO photoData = new PhotoDataInfoDTO();
            photoData.setRelatedId(relatedId);
            photoData.setIsOfflineUpload(0); // 默认为非离线
            photoData.setIsUploadedToBlob(1); // 默认已上传到Blob
            photoData.setModuleName(MODULE_NAME); // 模块名称 imei_upload
            photoData.setGuid(photo.getDetailId());

            // 设置上传者名称
            photoData.setUploaderName(userInfo.getEnglishName());
            photoData.setUploaderTime(photo.getUploadTime());

            photoData.setFdsUrl(photo.getUrl());
            photoData.setCreateTime(currentTime);
            photoData.setUpdateTime(currentTime);

            // 从URL中提取文件后缀
            String suffix = extractSuffixFromUrl(photo.getUrl());
            photoData.setSuffix(suffix);

            photoDataList.add(photoData);
        }

        // 批量插入图片数据
        if (CollectionUtils.isNotEmpty(photoDataList)) {
            fileUploadApiService.createPhotoData(photoDataList);
        }
    }

    /**
     * 从URL中提取文件后缀
     */
    private String extractSuffixFromUrl(String url) {
        if (StringUtils.isBlank(url)) {
            return ".jpg"; // 默认后缀
        }

        int lastDotIndex = url.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < url.length() - 1) {
            return url.substring(lastDotIndex);
        }

        return ".jpg"; // 默认后缀
    }

    @ApiDoc(description = "IMEI列表数据分页查询", value = "/api/so/queryImeiListByPage")
    @Override
    public CommonApiResponse<ImeiListQueryResp> queryImeiListByPage(ImeiListQueryReq request) {
        log.info("queryImeiListByPage start, request: {}", request);

        try {
            // 1. 入参必填字段校验
            if (StringUtils.isBlank(request.getCountryCode()) ||
                    StringUtils.isBlank(request.getUserId()) ||
                    request.getMiId() == null ||
                    request.getUserTitle() == null) {
                return new CommonApiResponse<>(400, "必填参数不能为空", null);
            }

            // 2. 判断userTitle，执行相应查询逻辑
            List<Integer> userStoreList = new ArrayList<>();
            if (!PROMOTER_TITLES.contains(request.getUserTitle())) {
                // 督导及其他角色，查询该用户关联的所有门店
                userStoreList = getUserStoreList(request.getMiId());
                if (userStoreList.isEmpty()) {
                    log.warn("用户没有关联的门店, miId: {}", request.getMiId());
                    return new CommonApiResponse<>(new ImeiListQueryResp());
                }
            }

            // 3. 构建查询条件并查询IMEI明细数据
            ImeiListQueryResp response = queryImeiDetailList(request, userStoreList);

            log.info("queryImeiListByPage success");
            return new CommonApiResponse<>(response);

        } catch (Exception e) {
            log.error("queryImeiListByPage error", e);
            return new CommonApiResponse<>(500, INTERNAL_ERROR + e.getMessage(), null);
        }
    }

    @ApiDoc(description = "IMEI明细详情查询", value = "/api/so/queryImeiDetail")
    @Override
    public CommonApiResponse<ImeiDetailQueryResp> queryImeiDetail(ImeiDetailQueryReq request) {
        log.info("queryImeiDetail start, request: {}", request);

        try {
            // 1. 入参校验
            if (StringUtils.isBlank(request.getImeiId()) ||
                    StringUtils.isBlank(request.getUserId()) ||
                    request.getMiId() == null ||
                    request.getUserTitle() == null) {
                return new CommonApiResponse<>(400, "必填参数不能为空", null);
            }

            // 2. 查询IMEI明细数据
            ImeiDetailQueryResp response = queryImeiDetailById(request.getImeiId());
            if (response == null) {
                return new CommonApiResponse<>(404, "IMEI明细不存在", null);
            }

            log.info("queryImeiDetail success");
            return new CommonApiResponse<>(response);

        } catch (Exception e) {
            log.error("queryImeiDetail error", e);
            return new CommonApiResponse<>(500, INTERNAL_ERROR + e.getMessage(), null);
        }
    }

    @ApiDoc(description = "IMEI汇总数据查询", value = "/api/so/queryImeiSummary")
    @Override
    public CommonApiResponse<ImeiSummaryQueryResp> queryImeiSummary(ImeiSummaryQueryReq request) {
        log.info("queryImeiSummary start, request: {}", request);

        try {
            // 1. 入参必填字段校验
            if (StringUtils.isBlank(request.getCountryCode()) ||
                    StringUtils.isBlank(request.getUserId()) ||
                    request.getMiId() == null ||
                    request.getUserTitle() == null) {
                return new CommonApiResponse<>(400, "必填参数不能为空", null);
            }

            // 2. 判断userTitle，执行相应查询逻辑
            List<Integer> userStoreList = new ArrayList<>();
            if (!PROMOTER_TITLES.contains(request.getUserTitle())) {
                // 督导及其他角色，查询该用户关联的所有门店
                userStoreList = getUserStoreList(request.getMiId());
                if (userStoreList.isEmpty()) {
                    log.warn("用户没有关联的门店, miId: {}", request.getMiId());
                    return new CommonApiResponse<>(new ImeiSummaryQueryResp());
                }
            }

            // 3. 查询IMEI汇总数据
            ImeiSummaryQueryResp response = queryImeiSummaryData(request, userStoreList);

            log.info("queryImeiSummary success");
            return new CommonApiResponse<>(response);

        } catch (Exception e) {
            log.error("queryImeiSummary error", e);
            return new CommonApiResponse<>(500, INTERNAL_ERROR + e.getMessage(), null);
        }
    }

    @Override
    public CommonApiResponse<Object> imeiBarcodeRead(ImeiBarcodeReadReq request) {
        if (request.getImage() == null || request.getImage().isEmpty()) {
            return new CommonApiResponse<>(400, "Image data cannot be empty", null);
        }

        try {
            Result result = BarcodeScannerUtil.decodeBarcode(request.getImage());
            String serialNumber = result.getText();
            log.info("imeiBarcodeRead serialNumber: {}", serialNumber);
            if (CharSequenceUtil.isBlank(serialNumber)) {
                return new CommonApiResponse<>(400, "SN/IMEI code cannot be empty", null);
            }
            SerialNumberType serialNumberType = SnImeiValidateUtil.getSerialNumberType(serialNumber);
            if (!(serialNumberType == SerialNumberType.IMEI || serialNumberType == SerialNumberType.SN)) {
                return new CommonApiResponse<>(400, "Serial code is not a valid SN/IMEI", null);
            }

            // 组装IMEI上报校验请求参数, 调用校验接口
            ImeiReportVerifyRequest imeiReportVerifyRequest = new ImeiReportVerifyRequest();
            imeiReportVerifyRequest.setSns(Collections.singletonList(serialNumber));
            imeiReportVerifyRequest.setCountryCode(request.getCountryCode());
            imeiReportVerifyRequest.setMiId(request.getMiId());
            imeiReportVerifyRequest.setUserTitle(request.getUserTitle());

            return imeiReportVerifyServiceImpl.imeiReportVerify(imeiReportVerifyRequest);

        } catch (NotFoundException e) {
            return new CommonApiResponse<>(400, "Barcode not found", null);
        } catch (Exception e) {
            return new CommonApiResponse<>(400, "Failed to parse barcode", null);
        }
    }

    /**
     * 获取用户关联的门店列表
     */
    private List<Integer> getUserStoreList(Long miId) {
        try {
            // 通过RmsStoreService查询用户关联的门店
            return rmsStoreService.getUserStoreIdsByMiId(miId);
        } catch (Exception e) {
            log.error("获取用户门店列表失败, miId: {}", miId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询IMEI明细列表
     */
    private ImeiListQueryResp queryImeiDetailList(ImeiListQueryReq request, List<Integer> userStoreList) {
        // 初始化分页参数
        PageInfo pageInfo = initPageInfo(request);

        // 获取时间筛选参数
        TimeRange timeRange = getTimeRange(request);

        // 查询明细数据
        List<Map<String, Object>> detailData = queryDetailData(request, userStoreList, pageInfo, timeRange);

        // 处理响应数据
        return buildResponse(request, userStoreList, pageInfo, detailData);
    }

    private PageInfo initPageInfo(ImeiListQueryReq request) {
        int pageIndex = request.getPageIndex() != null ? request.getPageIndex() : 1;
        int pageSize = request.getPageSize() != null ? request.getPageSize() : 10;
        int offset = (pageIndex - 1) * pageSize;
        return new PageInfo(pageIndex, pageSize, offset);
    }

    private TimeRange getTimeRange(ImeiListQueryReq request) {
        if (StringUtils.isBlank(request.getDateFilterType())) {
            return null;
        }

        if ("1".equals(request.getDateFilterType())) {
            return handleYearMonthFilter(request);
        } else if ("2".equals(request.getDateFilterType())) {
            return handleCustomDateRange(request);
        }
        return null;
    }

    private TimeRange handleYearMonthFilter(ImeiListQueryReq request) {
        if (StringUtils.isNotBlank(request.getYear()) && StringUtils.isNotBlank(request.getMonth())) {
            Long startTime = IntlTimeUtil.convertYearMonthToTimestamp(request.getYear(), request.getMonth(), request.getCountryCode(), true);
            Long endTime = IntlTimeUtil.convertYearMonthToTimestamp(request.getYear(), request.getMonth(), request.getCountryCode(), false);
            return new TimeRange(startTime, endTime);
        }
        return null;
    }

    private TimeRange handleCustomDateRange(ImeiListQueryReq request) {
        if (StringUtils.isNotBlank(request.getStartTime()) && StringUtils.isNotBlank(request.getEndTime())) {
            Long startTime = IntlTimeUtil.convertToCountryTimestamp(request.getStartTime(), request.getCountryCode());
            Long endTime = IntlTimeUtil.convertToCountryTimestamp(request.getEndTime(), request.getCountryCode());
            return new TimeRange(startTime, endTime);
        }
        return null;
    }

    private List<Map<String, Object>> queryDetailData(ImeiListQueryReq request, List<Integer> userStoreList,
                                                      PageInfo pageInfo, TimeRange timeRange) {
        return intlSoImeiMapper.queryImeiDetailList(
                request.getCountryCode(),
                PROMOTER_TITLES.contains(request.getUserTitle()) ? request.getMiId() : null,
                PROMOTER_TITLES.contains(request.getUserTitle()) ? null : userStoreList,
                request.getSearch(),
                request.getReportingType(),
                request.getStoreCode(),
                request.getProductLine(),
                request.getStoreType(),
                request.getChannelType(),
                request.getVerifyResult(),
                timeRange != null ? timeRange.getStartTime() : null,
                timeRange != null ? timeRange.getEndTime() : null,
                pageInfo.getOffset(),
                pageInfo.getPageSize() + 1 // 多查询一条用于判断是否有更多数据
        );
    }

    private ImeiListQueryResp buildResponse(ImeiListQueryReq request, List<Integer> userStoreList,
                                            PageInfo pageInfo, List<Map<String, Object>> detailData) {
        ImeiListQueryResp response = new ImeiListQueryResp();

        // 处理分页和明细数据
        processDetailList(response, detailData, pageInfo.getPageSize());

        // 处理日期分组数据
        processDateGroups(request, userStoreList, response);

        return response;
    }

    private void processDetailList(ImeiListQueryResp response, List<Map<String, Object>> detailData, int pageSize) {
        boolean hasMore = detailData.size() > pageSize;
        response.setMoreRecords(hasMore);

        List<ImeiListQueryResp.ImeiDetailItemDto> detailList = new ArrayList<>();
        int actualSize = hasMore ? pageSize : detailData.size();

        for (int i = 0; i < actualSize; i++) {
            detailList.add(convertToDetailItemDto(detailData.get(i)));
        }

        response.setDetailList(detailList);
    }

    private ImeiListQueryResp.ImeiDetailItemDto convertToDetailItemDto(Map<String, Object> item) {
        ImeiListQueryResp.ImeiDetailItemDto dto = new ImeiListQueryResp.ImeiDetailItemDto();
        dto.setId((BigInteger) item.get("id"));
        dto.setProductName((String) item.get("productName"));
        dto.setImei1Mask((String) item.get("imei1Mask"));
        dto.setImei2Mask((String) item.get("imei2Mask"));
        dto.setSnMask((String) item.get("snMask"));
        dto.setNote((String) item.get("note"));
        dto.setStoreName((String) item.get("storeName"));
        dto.setVerifyResult((Integer) item.get("verifyResult"));
        dto.setVerifyResultDetail((String) item.get("verifyResultDetail"));

        processSalesTime(dto, item);

        return dto;
    }

    private void processSalesTime(ImeiListQueryResp.ImeiDetailItemDto dto, Map<String, Object> item) {
        Long salesTimeTimestamp = (Long) item.get("sales_time");
        String countryCode = (String) item.get("country_code");

        if (salesTimeTimestamp != null) {
            String salesTimeStr = IntlTimeUtil.parseTimestampToAreaTime(countryCode, salesTimeTimestamp);
            if (StringUtils.isNotBlank(salesTimeStr)) {
                dto.setSalesTime(salesTimeStr);
                dto.setSalesDate(salesTimeStr.substring(0, 10));
            }
        }
    }

    private void processDateGroups(ImeiListQueryReq request, List<Integer> userStoreList, ImeiListQueryResp response) {
        List<Map<String, Object>> dateGroupData = buildDateGroupData(response.getDetailList());

        if (!dateGroupData.isEmpty()) {
            updateLastDateCount(request, userStoreList, dateGroupData);
        }

        response.setDateGroupList(convertToDateGroupDtos(dateGroupData));
    }

    private List<Map<String, Object>> buildDateGroupData(List<ImeiListQueryResp.ImeiDetailItemDto> detailList) {
        if (detailList.isEmpty()) {
            return new ArrayList<>();
        }

        Map<String, Integer> dateCountMap = new HashMap<>();
        for (ImeiListQueryResp.ImeiDetailItemDto item : detailList) {
            String salesDate = item.getSalesDate();
            if (StringUtils.isNotBlank(salesDate)) {
                dateCountMap.put(salesDate, dateCountMap.getOrDefault(salesDate, 0) + 1);
            }
        }

        return dateCountMap.entrySet().stream()
                .map(entry -> {
                    Map<String, Object> dateGroup = new HashMap<>();
                    dateGroup.put("date", entry.getKey());
                    dateGroup.put("count", entry.getValue());
                    return dateGroup;
                })
                .sorted((a, b) -> ((String) b.get("date")).compareTo((String) a.get("date")))
                .collect(Collectors.toList());
    }

    private void updateLastDateCount(ImeiListQueryReq request, List<Integer> userStoreList,
                                     List<Map<String, Object>> dateGroupData) {
        String lastDate = (String) dateGroupData.get(dateGroupData.size() - 1).get("date");
        Long lastDateTimestamp = IntlTimeUtil.parseAreaTimeToTimestamp(request.getCountryCode(), lastDate + " 00:00:00");

        int countForLastDate = intlSoImeiMapper.countImeiDetailList(
                request.getCountryCode(),
                PROMOTER_TITLES.contains(request.getUserTitle()) ? request.getMiId() : null,
                PROMOTER_TITLES.contains(request.getUserTitle()) ? null : userStoreList,
                request.getSearch(),
                request.getReportingType(),
                request.getStoreCode(),
                request.getProductLine(),
                request.getStoreType(),
                request.getChannelType(),
                request.getVerifyResult(),
                lastDateTimestamp,
                lastDateTimestamp + 24 * 60 * 60 * 1000
        );

        dateGroupData.get(dateGroupData.size() - 1).put("count", countForLastDate);
    }

    private List<ImeiListQueryResp.DateGroupDto> convertToDateGroupDtos(List<Map<String, Object>> dateGroupData) {
        return dateGroupData.stream()
                .map(item -> {
                    ImeiListQueryResp.DateGroupDto dto = new ImeiListQueryResp.DateGroupDto();
                    dto.setDate((String) item.get("date"));
                    dto.setCount(((Number) item.get("count")).intValue());
                    return dto;
                })
                .collect(Collectors.toList());
    }

    // Helper classes
    @Getter
    private static class PageInfo {
        private final int pageIndex;
        private final int pageSize;
        private final int offset;

        public PageInfo(int pageIndex, int pageSize, int offset) {
            this.pageIndex = pageIndex;
            this.pageSize = pageSize;
            this.offset = offset;
        }

    }

    @Getter
    private static class TimeRange {
        private final Long startTime;
        private final Long endTime;

        public TimeRange(Long startTime, Long endTime) {
            this.startTime = startTime;
            this.endTime = endTime;
        }

    }

    /**
     * 根据ID查询IMEI明细
     */
    private ImeiDetailQueryResp queryImeiDetailById(String imeiId) {
        List<Map<String, Object>> data = intlSoImeiMapper.queryImeiDetailById(imeiId);

        if (data.isEmpty()) {
            return null;
        }
        Map<String, Object> first = data.get(0);
        ImeiDetailQueryResp response = new ImeiDetailQueryResp();
        response.setId((BigInteger) first.get("id"));
        response.setProductName((String) first.get("productName"));
        response.setImei1Mask((String) first.get("imei1Mask"));
        response.setImei2Mask((String) first.get("imei2Mask"));
        response.setSnMask((String) first.get("snMask"));
        response.setNote((String) first.get("note"));
        response.setStoreName((String) first.get("storeName"));
        response.setCreatedBy((String) first.get("createdBy"));
        String createdOn = IntlTimeUtil.parseTimestampToFormatAreaTime("yyyy-MM-dd HH:mm:ss",
                (String) first.get("countryCode"),
                (Long) first.get("createdOn")
        );
        response.setCreatedOn(createdOn);
        response.setVerifyResultDetail((String) first.get("verifyResultDetail"));

        Integer verifyResult = (Integer) first.get("verifyResult");
        response.setVerifyResult(verifyResult);
        // 从枚举获取描述，取不到默认空值
        response.setVerifyResultLabel(
                Optional.ofNullable(VerificationResultEnum.getByValue(verifyResult))
                        .map(VerificationResultEnum::getName)
                        .orElse("")
        );
        Integer reportingType = (Integer) first.get("reportingType");
        response.setReportingType(reportingType);
        // 从枚举获取描述，取不到默认空值
        response.setReportingTypeLabel(
                Optional.ofNullable(ReportingTypeEnum.getByValue(reportingType))
                        .map(ReportingTypeEnum::getName)
                        .orElse("")
        );

        // 遍历数据,获取图片url
        List<String> photoUrls = new ArrayList<>();
        for (Map<String, Object> item : data) {
            String photoUrl = (String) item.get("url");
            if (StringUtils.isNotBlank(photoUrl)) {
                photoUrls.add(photoUrl);
            }
        }
        response.setPhotoUrls(photoUrls);

        return response;
    }

    /**
     * 查询IMEI汇总数据
     */
    private ImeiSummaryQueryResp queryImeiSummaryData(ImeiSummaryQueryReq request, List<Integer> userStoreList) {
        // 时间筛选参数
        Long startTime = null;
        Long endTime = null;
        if (StringUtils.isNotBlank(request.getDateFilterType())) {
            if ("1".equals(request.getDateFilterType()) &&
                    StringUtils.isNotBlank(request.getYear()) &&
                    StringUtils.isNotBlank(request.getMonth())) {
                // 年月筛选
                startTime = IntlTimeUtil.convertYearMonthToTimestamp(request.getYear(), request.getMonth(), request.getCountryCode(),
                        true);
                endTime = IntlTimeUtil.convertYearMonthToTimestamp(request.getYear(), request.getMonth(), request.getCountryCode(),
                        false);
            } else if ("2".equals(request.getDateFilterType()) &&
                    StringUtils.isNotBlank(request.getStartTime()) &&
                    StringUtils.isNotBlank(request.getEndTime())) {
                // 自定义时间段筛选
                startTime = IntlTimeUtil.convertToCountryTimestamp(request.getStartTime(), request.getCountryCode());
                endTime = IntlTimeUtil.convertToCountryTimestamp(request.getEndTime(), request.getCountryCode());
            }
        }

        // 查询汇总数据
        List<Map<String, Object>> summaryData = intlSoImeiMapper.queryImeiSummary(
                request.getCountryCode(),
                PROMOTER_TITLES.contains(request.getUserTitle()) ? request.getMiId() : null,
                PROMOTER_TITLES.contains(request.getUserTitle()) ? null : userStoreList,
                request.getSearch(),
                request.getStoreCode(),
                request.getProductLine(),
                request.getStoreType(),
                request.getChannelType(),
                request.getVerifyResult(),
                startTime,
                endTime);

        // 构建响应
        ImeiSummaryQueryResp response = new ImeiSummaryQueryResp();
        int totalCount = 0;
        int successCount = 0;
        int verifyingCount = 0;
        int failedCount = 0;

        for (Map<String, Object> item : summaryData) {
            Integer verificationResult = (Integer) item.get("verification_result");
            int count = ((Number) item.get("count")).intValue();
            totalCount += count;

            if (verificationResult != null) {
                switch (verificationResult) {
                    case 100000000: // Verifying
                        verifyingCount += count;
                        break;
                    case 100000002: // Successfully
                        successCount += count;
                        break;
                    case 100000001: // Failed
                    case 100000003: // TimeOut
                        failedCount += count;
                        break;
                    default:
                        log.warn("未知的验证结果: {}, count: {}", verificationResult, count);
                        break;
                }
            }
        }

        response.setTotalCount(totalCount);
        response.setSuccessCount(successCount);
        response.setVerifyingCount(verifyingCount);
        response.setFailedCount(failedCount);

        return response;
    }


}
