package com.mi.info.intl.retail.so.app.provider.upload.dto;

import com.mi.info.intl.retail.intlretail.service.api.so.upload.dto.QtyImportRowData;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * QTY校验结果类
 *
 * <AUTHOR>
 * @date 2025/1/28
 */
@Data
public class QtyValidationResult {

    /**
     * 校验后的数据列表
     */
    private List<QtyImportRowData> dataList;

    /**
     * 门店校验上下文映射 (key: storeCode, value: 校验上下文)
     */
    private Map<String, StoreValidationContext> storeContextMap;
} 