package com.mi.info.intl.retail.so.domain.rule.aggregate;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import com.google.common.collect.Sets;
import com.mi.info.intl.retail.constant.CommonConstant;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.request.QueryApproverListReq;
import com.mi.info.intl.retail.so.domain.sys.service.OrganizationUserService;
import com.mi.info.intl.retail.so.infra.database.dataobject.rule.IntlSoRuleRetailer;
import com.xiaomi.cnzone.commons.utils.DateUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.mi.info.intl.retail.api.country.CountryTimeZoneApiService;
import com.mi.info.intl.retail.api.country.dto.CountryDTO;
import com.mi.info.intl.retail.bean.PageDTO;
import com.mi.info.intl.retail.core.org.configuration.OrganizationPlatformConf;
import com.mi.info.intl.retail.core.org.service.OrganizePlatformService;
import com.mi.info.intl.retail.dto.LabelValueDTO;
import com.mi.info.intl.retail.enums.SwitchEnum;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.exception.ErrorCodes;
import com.mi.info.intl.retail.intlretail.service.api.bpm.BpmService;
import com.mi.info.intl.retail.intlretail.service.api.bpm.dto.BpmUser;
import com.mi.info.intl.retail.intlretail.service.api.bpm.dto.SoRuleFormDataEntity;
import com.mi.info.intl.retail.intlretail.service.api.bpm.enums.BpmApproveBusinessCodeEnum;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.SoRuleBpmBodyDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.*;
import com.mi.info.intl.retail.intlretail.service.api.so.sys.dto.DictSysDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.sys.request.DictSysRequest;
import com.mi.info.intl.retail.model.UserInfo;
import com.mi.info.intl.retail.service.DistributionLock;
import com.mi.info.intl.retail.service.DistributionLockService;
import com.mi.info.intl.retail.so.domain.rule.bpm.SoRuleBpmCallBack;
import com.mi.info.intl.retail.so.domain.rule.constants.CommonConstants;
import com.mi.info.intl.retail.so.domain.rule.constants.SoRuleDictKey;
import com.mi.info.intl.retail.so.domain.rule.enums.SoRuleDetailApproveStatus;
import com.mi.info.intl.retail.so.domain.rule.enums.SoRuleEnum;
import com.mi.info.intl.retail.so.domain.rule.service.IntlSoRuleChangeLogService;
import com.mi.info.intl.retail.so.domain.rule.service.IntlSoRuleDetailService;
import com.mi.info.intl.retail.so.domain.rule.service.IntlSoRuleRetailerService;
import com.mi.info.intl.retail.so.domain.sys.service.IntlSysDictService;
import com.mi.info.intl.retail.so.infra.database.dataobject.rule.IntlSoRuleDetail;
import com.mi.info.intl.retail.so.infra.database.dataobject.rule.IntlSoRuleDetailLog;
import com.mi.info.intl.retail.utils.AESGCMUtil;
import com.mi.info.intl.retail.utils.UserInfoUtil;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import com.xiaomi.newretail.common.tools.utils.UUIDUtils;
import com.xiaomi.nr.eiam.admin.vo.provider.user.UserSensitiveInfoResp;

import cn.hutool.core.lang.Tuple;
import lombok.extern.slf4j.Slf4j;

/**
 * so规则创建、修改聚合服务
 *
 * <AUTHOR>
 * @date 2025/7/28 16:27
 */
@Slf4j
@Service
public class SoRuleAggregateService {

    @Resource
    private IntlSoRuleDetailService intlSoRuleDetailService;

    @Resource
    private IntlSoRuleChangeLogService intlSoRuleChangeLogService;

    @Resource
    private IntlSoRuleRetailerService intlSoRuleRetailerService;

    @Resource
    private DistributionLockService distributionLockService;

    @Resource
    private SoRuleTransactionalAggregateService soRuleTransactionalAggregateService;

    @Resource
    private CountryTimeZoneApiService countryTimeZoneApiService;

    @Resource
    private BpmService bpmService;

    @Resource
    private IntlSysDictService intlSysDictService;

    @Resource
    private OrganizePlatformService organizePlatformService;

    @Resource
    private OrganizationPlatformConf organizationPlatformConf;

    @Resource
    private OrganizationUserService organizationUserService;

    public PageDTO<SoRuleDetailResultDTO> getRuleList(SoRuleDetailQueryDTO queryDto) {
        queryDto.setPageNum(Objects.nonNull(queryDto.getPageNum()) ? queryDto.getPageNum() : 1);
        queryDto.setPageSize(Objects.nonNull(queryDto.getPageSize()) ? queryDto.getPageSize() : 20);
        PageDTO<SoRuleDetailResultDTO> page = intlSoRuleDetailService.pageList(queryDto);
        List<SoRuleDetailResultDTO> records = page.getRecords();
        List<String> countryCodeList = queryDto.getCountryCodeList();
        if (CollectionUtils.isEmpty(countryCodeList) && CollectionUtils.isNotEmpty(records)) {
            countryCodeList = records.stream().map(SoRuleDetailResultDTO::getCountryCode).collect(Collectors.toList());
        }
        // 区域、国家名称赋值
        if (CollectionUtils.isEmpty(records)) {
            return page;
        }
        Map<String, CountryDTO> countryMap = countryTimeZoneApiService.getCountryInfoByCodeList(countryCodeList)
            .stream().collect(Collectors.toMap(CountryDTO::getCountryCode, Function.identity()));
        records.forEach(it -> setRuleDetailAnyLabels(it, countryMap.get(it.getCountryCode()),
            intlSoRuleChangeLogService.getRetailerDefaultSwitchDict()));
        return page;
    }

    /**
     * 校验当前国家下是否存在IMEI&QTY都未启用的零售商记录，若存在，返回false，不存在返回true
     *
     * @param ruleReqDto 国家编码
     * @return true:不存在IMEI&QTY都未启用的零售商记录；false:存在
     */
    public Boolean validateRuleRetailers(SoRuleBaseReqDTO ruleReqDto) {
        Tuple imeiQtySwitch = soRuleTransactionalAggregateService.getImeiAndQtySwitch(ruleReqDto.getAllAddImei(),
            ruleReqDto.getAllAddQty());
        SwitchEnum imeiSwitch = imeiQtySwitch.get(0);
        SwitchEnum qtySwitch = imeiQtySwitch.get(1);
        // 若传参IMEI&QTY开关都为关闭状态，且零售商列表为空，则直接返回false
        if (Objects.equals(SwitchEnum.OFF, imeiSwitch) && Objects.equals(SwitchEnum.OFF, qtySwitch)
            && CollectionUtils.isEmpty(ruleReqDto.getRetailerList())) {
            return false;
        }
        // 若传参IMEI&QTY开关有一个为全部开启状态，则直接返回true
        if (Objects.equals(SwitchEnum.ON, imeiSwitch) || Objects.equals(SwitchEnum.ON, qtySwitch)) {
            return true;
        }
        // 若传入的修改的零售商中存在IMEI&QTY都未启用的零售商，则直接返回false
        if (CollectionUtils.isNotEmpty(ruleReqDto.getRetailerList())) {
            long modifiedNoRuleCount = ruleReqDto.getRetailerList().stream()
                .filter(it -> Objects.equals(SwitchEnum.OFF.getValue(), it.getImeiSwitch())
                    && Objects.equals(SwitchEnum.OFF.getValue(), it.getQtySwitch()))
                .count();
            if (modifiedNoRuleCount > 0) {
                return false;
            }
        }
        List<String> excludeRetailerCodes = ruleReqDto.getRetailerList().stream()
            .map(SoRuleRetailerItemDTO::getRetailerCode).collect(Collectors.toList());
        // 统计当前国家下所有零售商的IMEI&QTY开关状态，IMEI&QTY都未启用的零售商数量；（主规则，不管是创建还是修改，都会基于主规则零售商数据进行判断）
        SoRuleRetailerStatisticsDTO retailerStatistics = intlSoRuleRetailerService.getRetailerStatisticsWithExcludes(
            ruleReqDto.getCountryCode(), SoRuleEnum.MASTER.getValue(), excludeRetailerCodes);
        Integer totalCount = retailerStatistics.getTotalRetailersCount();
        Integer dbNoRuleCount = retailerStatistics.getNoRuleRetailersCount();
        // 已无更多的零售商，则返回true
        if (totalCount == 0) {
            return true;
        }
        // 若当前国家下所有零售商的IMEI&QTY都未启用的零售商数量大于0，则返回false
        return totalCount > 0 && dbNoRuleCount <= 0;
    }

    /**
     * 创建、修改规则时，校验规则参数是否合法。合并校验逻辑
     * 
     * @param ruleValidateDto 校验参数
     * @return 校验成功返回true
     */
    public SoRuleValidateResultDTO validateRule(SoRuleBaseReqDTO ruleValidateDto) {
        // 校验国家
        validateCountryCode(ruleValidateDto.getCountryCode());
        // 填充imei、photo规则里的userTitle、productLine
        fillImeiAndPhotoRules(ruleValidateDto);
        // imei规则校验
        validateImeiRules(ruleValidateDto.getImeiRuleList());
        // 图片规则校验
        validatePhotoRules(ruleValidateDto.getPhotoRuleList());
        // 修改规则时，校验规则是否有变更，没有变更提示错误
        if (Objects.equals(ruleValidateDto.getType(), 2)) {
            validateRuleIfNotChanged(ruleValidateDto);
        }
        // 以下校验不影响提交，只是提示。按顺序校验并快速失败返回
        SoRuleValidateResultDTO validateResult = new SoRuleValidateResultDTO(null, true);
        // 校验零售商是否都开启IMEI或QTY规则
        boolean haveNoRuleRetailers = validateRuleRetailers(ruleValidateDto);
        if (!haveNoRuleRetailers) {
            validateResult.setScene(SoRuleValidateResultDTO.SCENE_RETAILER);
            validateResult.setValidate(false);
            return validateResult;
        }
        return validateResult;
    }

    /**
     * 获取规则详情
     *
     * @param ruleId 规则ID
     * @return 规则详情
     */
    public SoRuleDetailResultDTO getRuleDetail(Long ruleId) {
        IntlSoRuleDetail ruleDetail = intlSoRuleDetailService.getById(ruleId);
        if (Objects.isNull(ruleDetail)) {
            throw new BizException(ErrorCodes.SO_RULE_NOT_EXIST, ruleId);
        }
        SoRuleDetailResultDTO ruleDetailResult = intlSoRuleDetailService.convertToRuleDetailResult(ruleDetail);
        // 赋值国家、区域名称
        Optional<CountryDTO> countryOptional =
            countryTimeZoneApiService.getCountryInfoFromCache(ruleDetail.getCountryCode());
        setRuleDetailAnyLabels(ruleDetailResult, countryOptional.orElse(null),
            intlSoRuleChangeLogService.getRetailerDefaultSwitchDict());
        return ruleDetailResult;
    }

    /**
     * 创建规则流程
     *
     * @param soRuleDetailCreateDto 规则创建参数
     * @return 规则ID
     */
    public Long createRuleFlow(@NotNull SoRuleDetailCreateDTO soRuleDetailCreateDto) {
        soRuleDetailCreateDto.setType(1);
        // 基础校验&数据填充
        validateRule(soRuleDetailCreateDto);
        // 加分布式锁，处理创建规则逻辑
        try (DistributionLock ignore =
            distributionLockService.tryLock("so.rule.create.lock-{0}", soRuleDetailCreateDto.getCountryCode())) {
            // 校验当前国家是否已经存在规则，若存在，则返回错误提示
            boolean existRule = intlSoRuleDetailService.existRule(soRuleDetailCreateDto.getCountryCode());
            if (existRule) {
                throw new BizException(ErrorCodes.COUNTRY_EXISTS_RULE, soRuleDetailCreateDto.getCountryCode());
            }
            // 返回当前规则ID
            return soRuleTransactionalAggregateService.createRule(soRuleDetailCreateDto);
        }
    }

    public Long modifyRuleFlow(@NotNull SoRuleDetailModifyDTO soRuleDetailModifyDto) {
        soRuleDetailModifyDto.setType(2);
        // 基础校验&数据填充
        validateRule(soRuleDetailModifyDto);
        // 校验审批节点数据
        validateApproveNodes(soRuleDetailModifyDto);
        Long ruleCopyId;
        try (DistributionLock ignore =
            distributionLockService.tryLock("so.rule.modify.lock-{0}", soRuleDetailModifyDto.getCountryCode())) {
            // 是否存在审批中的规则修改流程，若存在，则返回错误提示
            boolean existRule = intlSoRuleChangeLogService.existApprovingRule(soRuleDetailModifyDto.getCountryCode());
            if (existRule) {
                throw new BizException(ErrorCodes.COUNTRY_EXISTS_APPROVING_RULE,
                    soRuleDetailModifyDto.getCountryCode());
            }
            ruleCopyId = soRuleTransactionalAggregateService.modifyRule(soRuleDetailModifyDto);
        }
        // 发起审批流程
        approveRuleLaunch(soRuleDetailModifyDto, ruleCopyId);
        return ruleCopyId;
    }

    /**
     * 发起审批流程
     *
     * @param soRuleDetailModifyDto 规则修改参数
     * @param ruleCopyId 规则副本ID
     */
    public void approveRuleLaunch(@NotNull SoRuleDetailModifyDTO soRuleDetailModifyDto, Long ruleCopyId) {
        IntlSoRuleDetailLog ruleDetailLog = intlSoRuleChangeLogService.getById(ruleCopyId);
        // 已发起审批流程，不允许重复发起审批流程
        if (StringUtils.isNotBlank(ruleDetailLog.getApprovalId())) {
            throw new BizException(ErrorCodes.COUNTRY_EXISTS_APPROVING_RULE, ruleDetailLog.getCountryCode());
        }
        UserInfo userInfo = UserInfoUtil.getUserContext();
        SoRuleFormDataEntity soRuleFormData = new SoRuleFormDataEntity();
        // 此处不可能为空，前面已校验
        CountryDTO countryInfo =
            countryTimeZoneApiService.getCountryInfoFromCache(ruleDetailLog.getCountryCode()).orElse(new CountryDTO());
        // Task Name : SO Rules Modification (Country Name)
        soRuleFormData.setTaskName(String.format("SO Rules Modification (%s)", countryInfo.getCountryName()));
        // 申请人：name(id)
        soRuleFormData.setApplicant(String.format("%s(%s)", userInfo.getUserName(), userInfo.getMiID()));
        // 申请时间
        soRuleFormData.setApplicationTime(IntlTimeUtil.getOffsetDateTimeByCountryCode(ruleDetailLog.getCountryCode()));
        // imei和photo规则
        setImeiAndPhotoRule(soRuleFormData, ruleDetailLog);
        // 零售商规则列表excel数据下载链接
        setRetailerRuleDataLink(soRuleFormData, ruleDetailLog);
        // 默认零售商规则开关
        Map<Integer, String> switchDict = intlSoRuleChangeLogService.getRetailerDefaultSwitchDict();
        String switchStr = soRuleDetailModifyDto.getDefaultRetailersSwitch().stream().map(switchDict::get)
            .collect(Collectors.joining(","));
        soRuleFormData.setDefaultRetailerSwitch(switchStr);
        // 生成businessKey
        String businessKey = UUIDUtils.randomUUID();
        // 将所选节点审批人放入变量中
        HashMap<String, Object> variables = Maps.newHashMap();
        // 两个节点的所选审批人，邮箱前缀。（前面校验逻辑里已赋值）
        List<ApproverDTO> approverList = soRuleDetailModifyDto.getApproverList();
        variables.put(CommonConstants.FIRST_APPROVER_KEY, approverList.get(1).getApproverEmailPrefix());
        variables.put(CommonConstants.SECOND_APPROVER_KEY, approverList.get(2).getApproverEmailPrefix());
        // 申请人
        String applicant = UserInfoUtil.getEmailPrefix(userInfo.getEmail());
        try {
            log.info("So rule modify approve launch. businessKey: {}, formData: {}, variables: {}", businessKey,
                JSON.toJSONString(soRuleFormData), variables);
            bpmService.create(soRuleFormData, BpmApproveBusinessCodeEnum.SO_RULE, applicant, businessKey, variables);
        } catch (Exception e) {
            log.error("So rule modify approve launch failed. businessKey: {}, formData: {}, variables: {}", businessKey,
                JSON.toJSONString(soRuleFormData), variables, e);
            throw new BizException(ErrorCodes.SO_RULE_LOG_APPROVAL_FAILED, ruleCopyId, "launch");
        }
        // 审批流程ID，绑定审批流程ID
        ruleDetailLog.setApprovalId(businessKey);
        // 审批表单数据，用户信息加密处理
        applicant = AESGCMUtil.encryptGCM(applicant);
        variables.entrySet().forEach(entry -> entry.setValue(AESGCMUtil.encryptGCM(String.valueOf(entry.getValue()))));
        soRuleFormData.setApplicant(AESGCMUtil.encryptGCM(soRuleFormData.getApplicant()));
        SoRuleBpmBodyDTO bpmBody = new SoRuleBpmBodyDTO();
        bpmBody.setApplicant(applicant);
        bpmBody.setBusinessKey(businessKey);
        bpmBody.setFormData(soRuleFormData);
        bpmBody.setApprovers(variables);
        ruleDetailLog.setBpmBody(JSON.toJSONString(bpmBody));
        // 发起人ID
        String approveId = String.valueOf(UserInfoUtil.getUserContext().getMiID());
        soRuleTransactionalAggregateService.approveRuleLaunch(ruleDetailLog, approveId);
    }

    /**
     * 审批通过回调，中间节点通过
     *
     * @param bpmCallBack 审批回调参数
     */
    public void approveRuleAgree(SoRuleApproveCallbackDTO bpmCallBack) {
        IntlSoRuleDetailLog ruleDetailLog = getAndValidateRuleDetailLog(bpmCallBack);
        IntlSoRuleDetail masterRule = getAndValidateMasterRule(ruleDetailLog.getMasterId());
        soRuleTransactionalAggregateService.approveRuleAgree(bpmCallBack, ruleDetailLog, masterRule);
    }

    /**
     * 审批完成回调 审批完成后，将当前修改规则的副本数据，更新到主规则数据中。并更新审批状态
     *
     * @param approveCompletedDTO 审批完成回调参数
     */
    public void approveRuleCompleted(SoRuleApproveCallbackDTO approveCompletedDTO) {
        IntlSoRuleDetailLog ruleDetailLog = getAndValidateRuleDetailLog(approveCompletedDTO);
        IntlSoRuleDetail masterRule = getAndValidateMasterRule(ruleDetailLog.getMasterId());
        soRuleTransactionalAggregateService.approveRuleCompleted(approveCompletedDTO, ruleDetailLog, masterRule);
    }

    /**
     * 审批拒绝回调
     *
     * @param bpmCallBack 审批拒绝回调参数
     */
    public void approveRuleRejected(SoRuleApproveCallbackDTO bpmCallBack) {
        IntlSoRuleDetailLog ruleDetailLog = getAndValidateRuleDetailLog(bpmCallBack);
        IntlSoRuleDetail masterRule = getAndValidateMasterRule(ruleDetailLog.getMasterId());
        soRuleTransactionalAggregateService.approveRuleRejected(bpmCallBack, ruleDetailLog, masterRule);
    }

    /**
     * 审批撤回回调
     *
     * @param modifyRecall 审批撤回回调参数
     */
    public Long approveRecalled(SoRuleDetailModifyRecallDTO modifyRecall) {
        Assert.notNull(modifyRecall.getId(), "Id can not be null.");
        UserInfo userInfo = UserInfoUtil.getUserContext();
        // 获取规则变更记录
        IntlSoRuleDetailLog ruleDetailLog = intlSoRuleChangeLogService.getById(modifyRecall.getId());
        if (Objects.isNull(ruleDetailLog)) {
            throw new BizException(ErrorCodes.SO_RULE_LOG_NOT_EXIST, modifyRecall.getId());
        }
        // 只有审批中流程，才能撤回
        if (SoRuleDetailApproveStatus.HAS_PENDING_APPROVAL_NO.contains(ruleDetailLog.getStatus())) {
            throw new BizException(ErrorCodes.SO_RULE_LOG_STATUS_ILLEGAL, ruleDetailLog.getId(),
                ruleDetailLog.getStatus());
        }

        // 调用bpm撤回接口(终止流程)
        try {
            log.info("Recall so rule approve flow. id: {}, approvalId: {}", ruleDetailLog.getId(),
                ruleDetailLog.getApprovalId());
            bpmService.terminate(ruleDetailLog.getApprovalId(), UserInfoUtil.getEmailPrefix(userInfo.getEmail()),
                modifyRecall.getComment());
        } catch (Exception e) {
            log.error("Recall so rule approve flow failed.  id: {}, approvalId: {}", ruleDetailLog.getId(),
                ruleDetailLog.getApprovalId(), e);
            throw new BizException(ErrorCodes.SO_RULE_LOG_APPROVAL_FAILED, ruleDetailLog.getId(), "recall");
        }
        // 转换成SoRuleApproveCallbackDTO，复用相应逻辑
        SoRuleApproveCallbackDTO approveCallback = new SoRuleApproveCallbackDTO();
        approveCallback.setBusinessKey(String.valueOf(modifyRecall.getId()));
        approveCallback.setApproveStatus(SoRuleDetailApproveStatus.RECALLED.getValue());
        approveCallback.setComment(modifyRecall.getComment());
        BpmUser approver = new BpmUser();
        approver.setPersonId(userInfo.getUserId());
        approver.setUserName(userInfo.getUserName());
        approveCallback.setAssignee(approver);
        // 更新规则副本记录状态
        IntlSoRuleDetail masterRule = getAndValidateMasterRule(ruleDetailLog.getMasterId());
        soRuleTransactionalAggregateService.approveRuleRecalled(approveCallback, ruleDetailLog, masterRule);
        return ruleDetailLog.getId();
    }

    private IntlSoRuleDetail getAndValidateMasterRule(Long masterId) {
        IntlSoRuleDetail masterRule = intlSoRuleDetailService.getById(masterId);
        if (Objects.isNull(masterRule)) {
            throw new BizException(ErrorCodes.SO_RULE_NOT_EXIST, masterId);
        }
        return masterRule;
    }

    private IntlSoRuleDetailLog getAndValidateRuleDetailLog(SoRuleApproveCallbackDTO approveCallback) {
        Assert.isTrue(StringUtils.isNotBlank(approveCallback.getBusinessKey()), "BusinessKey is null.");
        String approvalId = approveCallback.getBusinessKey();
        IntlSoRuleDetailLog ruleDetailLog = intlSoRuleChangeLogService.getByApprovalId(approvalId);
        if (Objects.isNull(ruleDetailLog)) {
            throw new BizException(ErrorCodes.SO_RULE_LOG_NOT_EXIST, approvalId);
        }
        // 校验状态是否合法
        if (!Objects.equals(ruleDetailLog.getStatus(), SoRuleDetailApproveStatus.PENDING.getValue())) {
            throw new BizException(ErrorCodes.SO_RULE_LOG_STATUS_ILLEGAL, ruleDetailLog.getId(),
                ruleDetailLog.getStatus());
        }
        // 解析节点审批人信息，更新审批节点状态
        updateApproveNodeStatus(ruleDetailLog, approveCallback);
        return ruleDetailLog;
    }

    /**
     * 更新审批节点状态
     *
     * @param ruleDetailLog 规则变更记录
     * @param approveCallback 审批回调参数
     */
    private void updateApproveNodeStatus(IntlSoRuleDetailLog ruleDetailLog, SoRuleApproveCallbackDTO approveCallback) {
        // 获取审批人信息
        BpmUser operator = Objects.nonNull(approveCallback.getAssignee()) ? approveCallback.getAssignee()
            : approveCallback.getOperator();
        if (Objects.isNull(operator)) {
            return;
        }
        String approverStr = ruleDetailLog.getApproverList();
        List<ApproverDTO> list = JSON.parseArray(approverStr, ApproverDTO.class);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        // 当前实际审批人
        ApproverDTO approveNode = null;
        // 更新对应节点的审批状态
        if (Objects.equals(approveCallback.getApproveNode(), SoRuleBpmCallBack.NODE1)) {
            approveNode = list.get(1);
        } else if (Objects.equals(approveCallback.getApproveNode(), SoRuleBpmCallBack.NODE2) && list.size() >= 2) {
            approveNode = list.get(2);
        }
        SoRuleDetailApproveStatus statusEnum = SoRuleDetailApproveStatus.parse(approveCallback.getApproveStatus());
        if (Objects.nonNull(approveNode)) {
            approveNode.setStatus(approveCallback.getApproveStatus());
            approveNode.setStatusLabel(Objects.nonNull(statusEnum) ? statusEnum.getDesc() : null);
            // 备注信息
            approveNode.setComment(approveCallback.getComment());
            approveNode.setApproveTime(DateUtils.getNowDate().getTime());
        }
        // 重新更新审批节点信息
        ruleDetailLog.setApproverList(JSON.toJSONString(list));
    }

    /**
     * 校验审批节点数据
     *
     * @param soRuleDetailModifyDto 审批节点列表
     */
    private void validateApproveNodes(SoRuleDetailModifyDTO soRuleDetailModifyDto) {
        String countryCode = soRuleDetailModifyDto.getCountryCode();
        List<ApproverDTO> approverList = soRuleDetailModifyDto.getApproverList();
        if (CollectionUtils.size(approverList) < 3) {
            throw new BizException(ErrorCodes.SO_RULE_APPROVE_NODE_ERROR);
        }
        QueryApproverListReq req = new QueryApproverListReq();
        req.setCountryCode(countryCode);
        List<ApproverDTO> allApprovers = organizationUserService.getApproverList(req);
        // 前置审批人校验，审批人邮箱为空，则业务异常，无法发起。
        Tuple approverEmailPrefix = validateApproverEmailPrefix(soRuleDetailModifyDto);
        // 审批节点校验
        for (int i = 0; i < approverList.size(); i++) {
            validateApproverNode(i, approverList.get(i), allApprovers.get(i), approverEmailPrefix);
        }
    }

    /**
     * 单个审批节点校验
     * 
     * @param nodeIndex 节点索引
     * @param selectedApprover 审批节点
     * @param originalApprover 原始审批节点（包含所有可选审批人）
     * @param approverEmailPrefix 审批人邮箱前缀
     */
    private void validateApproverNode(int nodeIndex, ApproverDTO selectedApprover, ApproverDTO originalApprover,
        Tuple approverEmailPrefix) {
        if (CollectionUtils.isEmpty(selectedApprover.getApproverList())) {
            throw new BizException(ErrorCodes.SO_RULE_APPROVE_NODE_ERROR);
        }
        String userFirst = approverEmailPrefix.get(0);
        String userSecond = approverEmailPrefix.get(1);
        if (nodeIndex == 1) {
            selectedApprover.setApproverEmailPrefix(userFirst);
        }
        if (nodeIndex == 2) {
            selectedApprover.setApproverEmailPrefix(userSecond);
        }
        // 校验审批人是否有效，userId+userName是否在审批人列表中
        List<ApproverInfo> originalList = originalApprover.getApproverList();
        // 将原始审批人列表建立快速匹配集合（userId#userName）
        Set<String> originalKeySet = originalList.stream().filter(Objects::nonNull)
            .map(it -> it.getUserId() + CommonConstant.Placeholder.SYMBOL_POUND + it.getUserName())
            .collect(Collectors.toSet());

        for (ApproverInfo selected : selectedApprover.getApproverList()) {
            if (Objects.isNull(selected) || Objects.isNull(selected.getUserId())
                || StringUtils.isBlank(selected.getUserName())) {
                throw new BizException(ErrorCodes.SO_RULE_APPROVE_NODE_ERROR);
            }
            String key = selected.getUserId() + CommonConstant.Placeholder.SYMBOL_POUND + selected.getUserName();
            if (!originalKeySet.contains(key)) {
                throw new BizException(ErrorCodes.SO_RULE_APPROVE_NODE_ERROR);
            }
        }
    }

    /**
     * 校验审批节点所选审批人邮箱是否存在，若不存在，则业务异常。无法发起审批流程。
     * 
     * @param soRuleDetailModifyDto 审批节点列表
     * @return Tuple<审批人1邮箱前缀，审批人2邮箱前缀>
     */
    private Tuple validateApproverEmailPrefix(SoRuleDetailModifyDTO soRuleDetailModifyDto) {
        Tuple approverEmailPrefix = getApproverInfo(soRuleDetailModifyDto);
        List<ApproverDTO> approverList = soRuleDetailModifyDto.getApproverList();
        // 审批人1
        String userFirst = approverEmailPrefix.get(0);
        // 审批人2
        String userSecond = approverEmailPrefix.get(1);
        if (StringUtils.isBlank(userFirst)) {
            throw new BizException(ErrorCodes.SO_RULE_APPROVE_NO_EMAIL, approverList.get(1).getNodeName());
        }
        if (StringUtils.isBlank(userSecond)) {
            throw new BizException(ErrorCodes.SO_RULE_APPROVE_NO_EMAIL, approverList.get(2).getNodeName());
        }
        return approverEmailPrefix;
    }

    /**
     * 校验photo规则列表
     *
     * @param photoRuleList photo规则列表
     */
    private void validatePhotoRules(List<PhotoRuleDTO> photoRuleList) {
        if (CollectionUtils.isEmpty(photoRuleList)) {
            return;
        }
        // 校验最多允许添加10条规则
        if (photoRuleList.size() > CommonConstants.MAX_RULES) {
            throw new BizException(ErrorCodes.RULES_GREATER_THAN_MAX, CommonConstants.MAX_RULES);
        }
        // 判重：检测Requiring Photo Rules 中 User Title是否存在重复
        // 解释：即不同规则里，User Title不能重复
        Map<PhotoRuleDTO, Set<String>> cartesianMap = Maps.newHashMap();
        for (PhotoRuleDTO photoRule : photoRuleList) {
            Set<String> combSet =
                photoRule.getUserTitleList().stream().map(UserTitleItemDTO::getUserTitleId).collect(Collectors.toSet());
            for (Set<String> combs : cartesianMap.values()) {
                if (combs.stream().anyMatch(combSet::contains)) {
                    throw new BizException(ErrorCodes.PHOTO_RULE_REPEAT);
                }
            }
            cartesianMap.put(photoRule, combSet);
        }
    }

    /**
     * imei规则校验
     *
     * @param imeiRuleList imei规则列表
     */
    private void validateImeiRules(List<ImeiRuleDTO> imeiRuleList) {
        if (CollectionUtils.isEmpty(imeiRuleList)) {
            return;
        }
        // 校验最多允许添加10条规则
        if (imeiRuleList.size() > CommonConstants.MAX_RULES) {
            throw new BizException(ErrorCodes.RULES_GREATER_THAN_MAX, CommonConstants.MAX_RULES);
        }

        // 判重，校验IMEI Activation Rules中 User Title，Activation Product Line两项是否存在重复规则
        // 解释：即不同规则里， User Title，Activation Product Line不能同时重复

        // 每个规则里，需要将userTitle和productLine进行组合，然后跟其他规则进行比较，如果存在重复，则校验失败
        Map<ImeiRuleDTO, Set<String>> cartesianMap = Maps.newHashMap();

        for (ImeiRuleDTO imeiRule : imeiRuleList) {
            final List<UserTitleItemDTO> userTitleList = CollectionUtils.isNotEmpty(imeiRule.getUserTitleList())
                ? imeiRule.getUserTitleList() : Lists.newArrayList();
            final List<ProductLineItemDTO> productLineList = CollectionUtils.isNotEmpty(imeiRule.getProductLineList())
                ? imeiRule.getProductLineList() : Lists.newArrayList();
            // 将userTitle和productLine进行组合；
            Set<String> combSet = userTitleList.stream()
                .flatMap(userTitleItem -> productLineList.stream()
                    .map(productLineItem -> userTitleItem.getUserTitleId() + productLineItem.getProductLineId()))
                .collect(Collectors.toSet());
            // 判断在cartesianMap中某个规则的userTitle和productLine组合是否存在交集（重复），若存在，则校验失败，返回错误。
            for (Set<String> combs : cartesianMap.values()) {
                if (combs.stream().anyMatch(combSet::contains)) {
                    throw new BizException(ErrorCodes.IMEI_RULE_REPEAT);
                }
            }
            // 若当前规则与前面的规则没有重复项，则将当前规则的userTitle和productLine组合放入cartesianMap中
            cartesianMap.put(imeiRule, combSet);
        }
        // Activate Time From， Activate Time To 范围在1~100
        for (ImeiRuleDTO it : imeiRuleList) {
            if (invalidActivationTime(it)) {
                throw new BizException(ErrorCodes.ACTIVATE_TIME_RANGE_ERROR);
            }
        }
    }

    /**
     * 校验激活时间范围是否有效
     *
     * @param imeiRule imei规则
     * @return 校验结果， 若为true，表示校验失败，若为false，表示校验成功
     */
    private boolean invalidActivationTime(ImeiRuleDTO imeiRule) {
        boolean activationTimeBeginError =
            Objects.nonNull(imeiRule.getBefore()) && (imeiRule.getBefore() > CommonConstants.MAX_EFFECTIVE_INTERVAL
                || imeiRule.getBefore() < CommonConstants.MIN_EFFECTIVE_INTERVAL);
        boolean activationTimeEndError =
            Objects.nonNull(imeiRule.getAfter()) && (imeiRule.getAfter() > CommonConstants.MAX_EFFECTIVE_INTERVAL
                || imeiRule.getAfter() < CommonConstants.MIN_EFFECTIVE_INTERVAL);
        return activationTimeBeginError || activationTimeEndError;
    }

    /**
     * 校验国家编码是否存在
     *
     * @param countryCode 国家编码
     */
    private void validateCountryCode(String countryCode) {
        Assert.isTrue(StringUtils.isNotBlank(countryCode), "CountryCode can't be blank.");
        Optional<CountryDTO> countryOptional = countryTimeZoneApiService.getCountryInfoFromCache(countryCode);
        if (!countryOptional.isPresent()) {
            log.error("country code {} not exist ", countryCode);
            throw new BizException(ErrorCodes.COUNTRY_CODE_NOT_EXIST, countryCode);
        }
    }

    /**
     * 兼容逻辑，由于前端在传userTitle、productLine时，不方便传对象结构（例："userTitleList":[{"userTitleId":"1","userTitleName":"1"}]）<br/>
     * ，所以前端传入的是结构为ID数组（例："userTitleList":["1","2"]<br/>
     * 此方法会根据传入的userTitles、productLines的ID数组，填充userTitleList,productLineList中的对象结构数据。<br/>
     * 且其他模块使用的是userTitleList,productLineList，所以此处需要填充userTitleList,productLineList<br/>
     *
     * @param soRuleDto 规则参数
     */
    private void fillImeiAndPhotoRules(SoRuleBaseReqDTO soRuleDto) {
        // 给userTitle、productLine名称赋值
        if (CollectionUtils.isEmpty(soRuleDto.getImeiRuleList())
            && CollectionUtils.isEmpty(soRuleDto.getPhotoRuleList())) {
            return;
        }
        DictSysRequest dictRequest = new DictSysRequest();
        ArrayList<DictSysDTO> dictCodeList = Lists.newArrayList();
        dictCodeList.add(new DictSysDTO(SoRuleDictKey.SO_RULE, SoRuleDictKey.JOB_TITLE));
        dictCodeList.add(new DictSysDTO(SoRuleDictKey.SO_RULE, SoRuleDictKey.PRODUCT_LINE));
        dictRequest.setDictCodeList(dictCodeList);
        Map<String, List<LabelValueDTO>> dictMap = intlSysDictService.getLabelValueListByDictCode(dictRequest);
        // 映射 id-> title
        Map<String, String> jobTitleMap = dictMap.get(SoRuleDictKey.JOB_TITLE).stream()
            .collect(Collectors.toMap(LabelValueDTO::getId, LabelValueDTO::getTitle));
        Map<String, String> productLineMap = dictMap.get(SoRuleDictKey.PRODUCT_LINE).stream()
            .collect(Collectors.toMap(LabelValueDTO::getId, LabelValueDTO::getTitle));

        fillImeiRules(soRuleDto, jobTitleMap, productLineMap);
        fillPhotoRules(soRuleDto, jobTitleMap);
    }

    private void fillPhotoRules(SoRuleBaseReqDTO soRuleDto, Map<String, String> jobTitleMap) {
        List<PhotoRuleDTO> photoRuleList = soRuleDto.getPhotoRuleList();
        if (CollectionUtils.isEmpty(photoRuleList)) {
            return;
        }
        for (PhotoRuleDTO photoRule : photoRuleList) {
            // user title
            List<String> userTitles = CollectionUtils.isNotEmpty(photoRule.getUserTitles()) ? photoRule.getUserTitles()
                : Lists.newArrayList();
            List<String> userTitleLabels = Lists.newArrayList();
            List<UserTitleItemDTO> userTitleList = Lists.newArrayList();
            for (String userTitle : userTitles) {
                userTitleLabels.add(jobTitleMap.get(userTitle));
                userTitleList.add(new UserTitleItemDTO(userTitle, jobTitleMap.get(userTitle)));
            }
            photoRule.setUserTitleLabels(userTitleLabels);
            photoRule.setUserTitleList(userTitleList);
        }
    }

    private void fillImeiRules(SoRuleBaseReqDTO soRuleDto, Map<String, String> jobTitleMap,
        Map<String, String> productLineMap) {
        List<ImeiRuleDTO> imeiRuleList = soRuleDto.getImeiRuleList();
        if (CollectionUtils.isEmpty(imeiRuleList)) {
            return;
        }
        for (ImeiRuleDTO imeiRule : imeiRuleList) {
            // user title (id)
            List<String> userTitles =
                CollectionUtils.isNotEmpty(imeiRule.getUserTitles()) ? imeiRule.getUserTitles() : Lists.newArrayList();
            List<String> userTitleLabels = Lists.newArrayList();
            List<UserTitleItemDTO> userTitleList = Lists.newArrayList();
            for (String userTitle : userTitles) {
                userTitleLabels.add(jobTitleMap.get(userTitle));
                userTitleList.add(new UserTitleItemDTO(userTitle, jobTitleMap.get(userTitle)));
            }
            imeiRule.setUserTitleLabels(userTitleLabels);
            imeiRule.setUserTitleList(userTitleList);
            // product line (id
            List<String> productLines = CollectionUtils.isNotEmpty(imeiRule.getProductLines())
                ? imeiRule.getProductLines() : Lists.newArrayList();
            List<String> productLineLabels = Lists.newArrayList();
            List<ProductLineItemDTO> productLineList = Lists.newArrayList();
            for (String productLine : productLines) {
                productLineLabels.add(productLineMap.get(productLine));
                productLineList.add(new ProductLineItemDTO(productLine, productLineMap.get(productLine)));
            }
            imeiRule.setProductLineLabels(productLineLabels);
            imeiRule.setProductLineList(productLineList);
        }
    }

    /**
     * 发起审批流程时，将当前修改的零售商规则副本数据，生成excel文件并上传到fds，并返回文档下载链接，设置到审批表单中。
     *
     * @param soRuleFormData 审批表单
     * @param ruleDetailLog ruleDetailLog
     */
    private void setRetailerRuleDataLink(SoRuleFormDataEntity soRuleFormData, IntlSoRuleDetailLog ruleDetailLog) {
        String link = intlSoRuleRetailerService.exportRetailerForApproveFlow(ruleDetailLog.getId(),
            ruleDetailLog.getCountryCode());
        soRuleFormData.setLink(link);
    }

    /**
     * 发起审批表单数据，设置imei和photo规则
     *
     * @param soRuleFormData 审批表单
     * @param ruleDetailLog ruleDetailLog
     */
    private void setImeiAndPhotoRule(SoRuleFormDataEntity soRuleFormData, IntlSoRuleDetailLog ruleDetailLog) {
        List<ImeiRuleDTO> imeiRuleList = StringUtils.isNotBlank(ruleDetailLog.getImeiRuleList())
            ? JSON.parseArray(ruleDetailLog.getImeiRuleList(), ImeiRuleDTO.class) : Lists.newArrayList();
        List<PhotoRuleDTO> photoRuleList = StringUtils.isNotBlank(ruleDetailLog.getPhotoRuleList())
            ? JSON.parseArray(ruleDetailLog.getPhotoRuleList(), PhotoRuleDTO.class) : Lists.newArrayList();
        List<SoRuleFormDataEntity.ImeiActivationRule> imeiRuleTab = imeiRuleList.stream().map(it -> {
            List<UserTitleItemDTO> userTitleList =
                CollectionUtils.isNotEmpty(it.getUserTitleList()) ? it.getUserTitleList() : Lists.newArrayList();
            String userTitles =
                userTitleList.stream().map(UserTitleItemDTO::getUserTitle).collect(Collectors.joining(","));
            List<ProductLineItemDTO> productLineList =
                CollectionUtils.isNotEmpty(it.getProductLineList()) ? it.getProductLineList() : Lists.newArrayList();
            String productLines =
                productLineList.stream().map(ProductLineItemDTO::getProductLine).collect(Collectors.joining(","));
            SoRuleFormDataEntity.ImeiActivationRule rule = new SoRuleFormDataEntity.ImeiActivationRule();
            rule.setUserTitle(userTitles);
            rule.setCategory(productLines);
            rule.setTimeFrom(String.valueOf(it.getBefore()));
            rule.setTimeTo(String.valueOf(it.getAfter()));
            return rule;
        }).collect(Collectors.toList());

        List<SoRuleFormDataEntity.RequiringPhotoRule> photoRuleTab = photoRuleList.stream().map(it -> {
            List<UserTitleItemDTO> userTitleList =
                CollectionUtils.isNotEmpty(it.getUserTitleList()) ? it.getUserTitleList() : Lists.newArrayList();
            String userTitles =
                userTitleList.stream().map(UserTitleItemDTO::getUserTitle).collect(Collectors.joining(","));
            SoRuleFormDataEntity.RequiringPhotoRule photoRuleTable = new SoRuleFormDataEntity.RequiringPhotoRule();
            photoRuleTable.setUserTitle(userTitles);
            photoRuleTable.setImeiRequirePhoto(SwitchEnum.convert(it.getImeiRequirePhoto()).getDescription());
            photoRuleTable.setQtyRequirePhoto(SwitchEnum.convert(it.getQtyRequirePhoto()).getDescription());
            return photoRuleTable;
        }).collect(Collectors.toList());

        soRuleFormData.setImeiActivationRuleList(imeiRuleTab);
        soRuleFormData.setRequiringPhotoRuleList(photoRuleTab);
    }

    /**
     * 发起审批流程时，获取所选节点审批人信息<br/>
     * 第一个节点为发起节点，后续为审批节点； 此方法取后续审批节点的审批人信息<br/>
     *
     * @param soRuleDetailModifyDto 规则修改参数
     */
    private Tuple getApproverInfo(SoRuleDetailModifyDTO soRuleDetailModifyDto) {
        // 已前置校验，审批节点最少3个，每个节点审批人不可为空。
        List<ApproverDTO> approverList = soRuleDetailModifyDto.getApproverList();
        ApproverDTO firstApprover = CollectionUtils.size(approverList) > 1 ? approverList.get(1) : new ApproverDTO();
        ApproverInfo firstApproverInfo = CollectionUtils.isNotEmpty(firstApprover.getApproverList())
            ? firstApprover.getApproverList().get(0) : new ApproverInfo();
        ApproverDTO secondApprover = approverList.size() > 2 ? approverList.get(2) : new ApproverDTO();
        ApproverInfo secondApproverInfo = CollectionUtils.isNotEmpty(secondApprover.getApproverList())
            ? secondApprover.getApproverList().get(0) : new ApproverInfo();

        List<Long> approverIds = Lists.newArrayList(firstApproverInfo.getUserId(), secondApproverInfo.getUserId())
            .stream().filter(Objects::nonNull).map(Long::parseLong).collect(Collectors.toList());
        // 从组织中台查询审批用户信息。传参不超过5个
        List<UserSensitiveInfoResp> userInfos =
            organizePlatformService.getBatchUserInfo(approverIds, organizationPlatformConf.getScene());
        userInfos = CollectionUtils.isNotEmpty(userInfos) ? userInfos : Lists.newArrayList();
        Map<String, UserSensitiveInfoResp> userMap =
            userInfos.stream().collect(Collectors.toMap(it -> String.valueOf(it.getMiId()), Function.identity()));
        UserSensitiveInfoResp firstUser = userMap.get(firstApproverInfo.getUserId());
        UserSensitiveInfoResp secondUser = userMap.get(secondApproverInfo.getUserId());
        String userFirst = Objects.nonNull(firstUser) ? UserInfoUtil.getEmailPrefix(firstUser.getEmail()) : null;
        String userSecond = Objects.nonNull(secondUser) ? UserInfoUtil.getEmailPrefix(secondUser.getEmail()) : null;
        return new Tuple(userFirst, userSecond);
    }

    /**
     * 设置国家、区域、默认开关的展示名称
     *
     * @param ruleDetailResult 规则详情返回
     * @param country 国家
     * @param switchDict 零售商默认开关字典
     */
    private void setRuleDetailAnyLabels(SoRuleDetailResultDTO ruleDetailResult, CountryDTO country,
        Map<Integer, String> switchDict) {
        if (Objects.nonNull(country)) {
            ruleDetailResult.setCountryName(country.getCountryName());
            ruleDetailResult.setRegionName(country.getArea());
        }
        List<String> defaultRetailersSwitchLabels =
            ruleDetailResult.getDefaultRetailersSwitch().stream().map(switchDict::get).collect(Collectors.toList());
        ruleDetailResult.setDefaultRetailersSwitchLabels(defaultRetailersSwitchLabels);
    }

    /**
     * 校验规则是否有变更，没有变更提示错误，无意义<br/>
     * 检测变化的规则：默认零售商IMEI/QTY开关、IMEI规则、图片规则、零售商规则，若有一项有变化，则快速返回<br/>
     *
     * @param modifyDto 规则修改参数
     */
    private void validateRuleIfNotChanged(SoRuleBaseReqDTO modifyDto) {
        IntlSoRuleDetail masterRule =
            soRuleTransactionalAggregateService.getMasterRule(modifyDto.getRuleId(), modifyDto.getCountryCode());
        boolean defaultRetailersSwitchChanged =
            defaultRetailerSwitchChanged(masterRule.getDefaultRetailersSwitch(), modifyDto.getDefaultRetailersSwitch());
        if (defaultRetailersSwitchChanged) {
            return;
        }
        boolean imeiRuleChanged = imeiRuleChanged(masterRule.getImeiRuleList(), modifyDto.getImeiRuleList());
        if (imeiRuleChanged) {
            return;
        }
        boolean photoRuleChanged = photoRuleChanged(masterRule.getPhotoRuleList(), modifyDto.getPhotoRuleList());
        if (photoRuleChanged) {
            return;
        }
        boolean retailersChanged = retailerChanged(masterRule.getCountryCode(), modifyDto);
        if (retailersChanged) {
            return;
        }
        // 若无变更，则提示错误
        throw new BizException(ErrorCodes.SO_RULE_NO_CHANGED);
    }

    /**
     * IMEI&QTY开关变更判断
     *
     * @param defaultRetailersSwitchStr 主规则中的IMEI&QTY开关json字符串
     * @param modifyRetailersSwitch 变更的IMEI&QTY开关列表
     * @return true:IMEI&QTY开关有变更；false:IMEI&QTY开关无变更
     */
    private boolean defaultRetailerSwitchChanged(String defaultRetailersSwitchStr,
        List<Integer> modifyRetailersSwitch) {
        List<Integer> masterRetailersSwitch = JSON.parseArray(defaultRetailersSwitchStr, Integer.class);
        if (CollectionUtils.isEmpty(masterRetailersSwitch) && CollectionUtils.isEmpty(modifyRetailersSwitch)) {
            return false;
        }
        return !SetUtils.isEqualSet(Sets.newHashSet(masterRetailersSwitch), Sets.newHashSet(modifyRetailersSwitch));
    }

    /**
     * 零售商规则变更判断
     *
     * @param countryCode 规则所属国家
     * @param modifyDto 规则变更参数
     * @return true:零售商规则有变更；false:零售商规则无变更
     */
    private boolean retailerChanged(String countryCode, SoRuleBaseReqDTO modifyDto) {
        List<SoRuleRetailerItemDTO> retailerList = CollectionUtils.isNotEmpty(modifyDto.getRetailerList())
            ? modifyDto.getRetailerList() : Lists.newArrayList();
        Tuple imeiAndQtySwitch = soRuleTransactionalAggregateService.getImeiAndQtySwitch(modifyDto.getAllAddImei(),
            modifyDto.getAllAddQty());
        SwitchEnum imeiSwitch = imeiAndQtySwitch.get(0);
        SwitchEnum qtySwitch = imeiAndQtySwitch.get(1);
        if (Objects.nonNull(imeiSwitch) || Objects.nonNull(qtySwitch)) {
            return true;
        }
        List<String> retailerCodes =
            retailerList.stream().map(SoRuleRetailerItemDTO::getRetailerCode).collect(Collectors.toList());
        List<IntlSoRuleRetailer> dbMasterRetailers = intlSoRuleRetailerService
            .getByCountryCodeAndRetailerCodes(countryCode, SoRuleEnum.MASTER.getValue(), retailerCodes);
        for (IntlSoRuleRetailer dbRetailer : dbMasterRetailers) {
            SoRuleRetailerItemDTO retailer =
                retailerList.stream().filter(it -> Objects.equals(it.getRetailerCode(), dbRetailer.getRetailerCode()))
                    .findFirst().orElse(null);
            if (Objects.isNull(retailer) || !Objects.equals(retailer.getImeiSwitch(), dbRetailer.getImeiSwitch())
                || !Objects.equals(retailer.getQtySwitch(), dbRetailer.getQtySwitch())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 图片规则变更判断
     *
     * @param photoRuleListStr 主规则中的图片规则json字符串
     * @param modifyPhotoRuleList 修改的图片规则列表
     * @return true:图片规则有变更；false:图片规则无变更
     */
    private boolean photoRuleChanged(String photoRuleListStr, List<PhotoRuleDTO> modifyPhotoRuleList) {
        List<PhotoRuleDTO> masterPhotoRuleList = JSON.parseArray(photoRuleListStr, PhotoRuleDTO.class);
        if (CollectionUtils.isEmpty(masterPhotoRuleList) && CollectionUtils.isEmpty(modifyPhotoRuleList)) {
            return false;
        }
        if (masterPhotoRuleList.size() != modifyPhotoRuleList.size()) {
            return true;
        }
        List<PhotoRuleDTO> modifyCopyList = Lists.newArrayList(modifyPhotoRuleList);
        Iterator<PhotoRuleDTO> iterator = modifyCopyList.iterator();
        for (PhotoRuleDTO masterRule : masterPhotoRuleList) {
            boolean isSame = false;
            while (iterator.hasNext()) {
                PhotoRuleDTO modifyRule = iterator.next();
                if (isSamePhotoRuleItem(masterRule, modifyRule)) {
                    isSame = true;
                    iterator.remove();
                    break;
                }
            }
            if (!isSame) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断两个图片规则项是否相同
     * 
     * @param masterRule 主规则项
     * @param modifyRule 修改规则项
     * @return 是否一致，一致返回true，否则返回false
     */
    private boolean isSamePhotoRuleItem(PhotoRuleDTO masterRule, PhotoRuleDTO modifyRule) {
        // 比较userTitles
        Set<String> masterUserTitles = Sets.newHashSet(masterRule.getUserTitles());
        Set<String> modifyUserTitles = Sets.newHashSet(modifyRule.getUserTitles());
        return SetUtils.isEqualSet(masterUserTitles, modifyUserTitles)
            && Objects.equals(masterRule.getImeiRequirePhoto(), modifyRule.getImeiRequirePhoto())
            && Objects.equals(masterRule.getQtyRequirePhoto(), modifyRule.getQtyRequirePhoto());
    }

    /**
     * imei规则变更判断
     *
     * @param imeiRuleListStr 主规则中的imei规则json字符串
     * @param modifyImeiRuleList 修改的imei规则列表
     * @return true:imei规则有变更；false:imei规则无变更
     */
    private boolean imeiRuleChanged(String imeiRuleListStr, List<ImeiRuleDTO> modifyImeiRuleList) {
        List<ImeiRuleDTO> masterImeiRuleList = JSON.parseArray(imeiRuleListStr, ImeiRuleDTO.class);
        if (CollectionUtils.isEmpty(masterImeiRuleList) && CollectionUtils.isEmpty(modifyImeiRuleList)) {
            return false;
        }
        if (masterImeiRuleList.size() != modifyImeiRuleList.size()) {
            return true;
        }
        List<ImeiRuleDTO> modifyCopyList = Lists.newArrayList(modifyImeiRuleList);
        Iterator<ImeiRuleDTO> iterator = modifyCopyList.iterator();
        for (ImeiRuleDTO masterRule : masterImeiRuleList) {
            boolean isSame = false;
            while (iterator.hasNext()) {
                ImeiRuleDTO modifyRule = iterator.next();
                if (isSameImeiRuleItem(masterRule, modifyRule)) {
                    isSame = true;
                    iterator.remove();
                    break;
                }
            }
            if (!isSame) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断两个imei规则项是否相同
     * 
     * @param masterRule 主规则项
     * @param modifyRule 修改规则项
     * @return 是否一致，一致返回true，否则返回false
     */
    private boolean isSameImeiRuleItem(ImeiRuleDTO masterRule, ImeiRuleDTO modifyRule) {
        // 比较userTitles
        Set<String> masterUserTitles = Sets.newHashSet(masterRule.getUserTitles());
        Set<String> modifyUserTitles = Sets.newHashSet(modifyRule.getUserTitles());
        // 比较productLines
        Set<String> masterProductLines = Sets.newHashSet(masterRule.getProductLines());
        Set<String> modifyProductLines = Sets.newHashSet(modifyRule.getProductLines());
        return SetUtils.isEqualSet(masterUserTitles, modifyUserTitles)
            && SetUtils.isEqualSet(masterProductLines, modifyProductLines)
            && Objects.equals(masterRule.getBefore(), modifyRule.getBefore())
            && Objects.equals(masterRule.getAfter(), modifyRule.getAfter());
    }

}
