package com.mi.info.intl.retail.so.domain.rule.bean;

import java.util.List;

import com.mi.info.intl.retail.api.so.rule.resp.GetRetailRuleResp;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.PhotoRuleDTO;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDocClassDefine;

import lombok.Getter;
import lombok.Setter;

/**
 * 得到零售商销售规则
 *
 * <AUTHOR>
 * @date 2025/07/29
 */
@Getter
@Setter
public class GetRetailerSoRuleResp extends GetRetailRuleResp {

    /**
     * 国家编码
     */
    @ApiDocClassDefine("国家编码")
    private String countryCode;

    /**
     * 零售商编码
     */
    @ApiDocClassDefine("零售商编码")
    private String retailerCode;

    /**
     * 门店编码
     */
    @ApiDocClassDefine("门店编码")
    private String storeCode;

    /**
     * 是否启用imei
     */
    @ApiDocClassDefine("是否启用imei")
    private Integer enableImei;

    /**
     * 是否启用qty
     */
    @ApiDocClassDefine("是否启用qty")
    private Integer enableQty;

    /**
     * imei规则是否需要照片
     */
    @ApiDocClassDefine("imei规则是否需要照片")
    private boolean imeiRequirePhoto;

    /**
     * qty规则是否需要照片
     */
    @ApiDocClassDefine("qty规则是否需要照片")
    private boolean qtyRequirePhoto;

    /**
     * 规则ID
     */
    @ApiDocClassDefine("规则ID")
    private Long ruleId;

    /**
     * 照片规则列表
     */
    @ApiDocClassDefine("照片规则列表")
    private List<PhotoRuleDTO> photoRuleList;

}