package com.mi.info.intl.retail.so.domain.upload.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.info.intl.retail.intlretail.service.api.so.sys.dto.SkuAvailableDetailList;
import com.mi.info.intl.retail.intlretail.service.api.so.sys.dto.SkuAvailableSearch;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSkuAvailable;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【intl_sku_available(SKU 可用性信息表)】的数据库操作Service
* @createDate 2025-07-24 20:05:34
*/
public interface IntlSkuAvailableService extends IService<IntlSkuAvailable> {

    List<SkuAvailableDetailList> getDataBySearch(SkuAvailableSearch search);

    int getCountBySearch(SkuAvailableSearch search);

    void initZeroSalesTimeData();
}
