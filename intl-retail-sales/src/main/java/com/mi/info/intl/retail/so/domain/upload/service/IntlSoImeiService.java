package com.mi.info.intl.retail.so.domain.upload.service;

import com.mi.info.intl.retail.so.app.mq.dto.RmsSyncImeiData;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoImei;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【intl_so_imei(销量imei)】的数据库操作Service
 * @createDate 2025-07-25 16:36:39
 */
public interface IntlSoImeiService extends IService<IntlSoImei> {

    IntlSoImei checkImeiExist(Long id, String rmsId);

    IntlSoImei getIntlSoImeiByCondition(Long id, String rmsId);

    List<IntlSoImei> batchGetByIds(List<Long> ids);

    void doImeiSave(RmsSyncImeiData data);

    void doImeiUpdate(String operatorType, List<String> fields, RmsSyncImeiData data);

    List<IntlSoImei> getNeedVerifyImeiList(Integer verifyState, Integer verifyResult, Integer limit);

    int batchSoftUpdateById(List<IntlSoImei> intlSoImeiList);

    // 批量保存IMEI数据
    void doImeiBatchSave(List<RmsSyncImeiData> imeiDataList);

    // 批量更新IMEI数据
    void doImeiBatchUpdate(String operateType, List<RmsSyncImeiData> imeiDataList);

    // 批量检查IMEI是否存在
    List<IntlSoImei> batchGetImeiByRmsId(List<Long> retailIds, List<String> rmsIds);

}
