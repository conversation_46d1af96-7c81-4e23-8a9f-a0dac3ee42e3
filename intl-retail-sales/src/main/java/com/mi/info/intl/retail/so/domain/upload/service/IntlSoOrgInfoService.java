package com.mi.info.intl.retail.so.domain.upload.service;

import com.mi.info.intl.retail.api.front.position.dto.PositionStoreInfoDTO;
import com.mi.info.intl.retail.api.front.store.dto.StoreChangeLogDto;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.ImeiDetailDto;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoOrgInfo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【intl_so_org_info(销量阵地信息)】的数据库操作Service
 * @createDate 2025-07-25 16:40:50
 */
public interface IntlSoOrgInfoService extends IService<IntlSoOrgInfo> {

    List<IntlSoOrgInfo> batchGetByIds(List<Long> idList);

    Map<Long, IntlSoOrgInfo> batchGetByIdsMap(List<Long> idList);

    /**
     * 创建门店信息数据
     * @param positionStoreInfo
     * @param countryCode
     * @return
     */
    Long createOrgInfo(PositionStoreInfoDTO positionStoreInfo, String countryCode);

    /**
     * 创建门店信息数据（支持门店变更日志）
     * @param positionStoreInfo 阵地门店信息
     * @param countryCode 国家代码
     * @param storeLogMap 门店变更日志映射
     * @param positionCode 阵地代码
     * @param salesTime 销售时间
     * @return 门店信息ID
     */
    Long createOrgInfoWithStoreLog(PositionStoreInfoDTO positionStoreInfo, String countryCode,
                                   Map<String, StoreChangeLogDto> storeLogMap, String positionCode, Long salesTime);

    /**
     * 批量查询门店变更日志
     * @param detailList IMEI明细列表
     * @param positionCode 阵地代码
     * @return 门店变更日志映射
     */
    Map<String, StoreChangeLogDto> batchQueryStoreChangeLogs(List<ImeiDetailDto> detailList, String positionCode);

}
