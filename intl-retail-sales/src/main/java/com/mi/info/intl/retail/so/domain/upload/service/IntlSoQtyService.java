package com.mi.info.intl.retail.so.domain.upload.service;

import com.mi.info.intl.retail.so.app.mq.dto.RmsSyncQtyData;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoQty;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【intl_so_qty(销量qty)】的数据库操作Service
 * @createDate 2025-07-24 19:21:34
 */
public interface IntlSoQtyService extends IService<IntlSoQty> {

    IntlSoQty getIntlSoQtyByRmsId(String rmsId);

    void doQtyUpdate(RmsSyncQtyData data);

    void doQtySave(RmsSyncQtyData data);
    IntlSoQty getById(Long id);

    void doQtyBatchSave(List<RmsSyncQtyData> qtyDataList);

    void doQtyBatchUpdate(List<RmsSyncQtyData> qtyDataList);

    List<IntlSoQty> batchGetQtyByRmsId(List<String> rmsIds);

    List<IntlSoQty> getByIds(List<Long> ids);
}
