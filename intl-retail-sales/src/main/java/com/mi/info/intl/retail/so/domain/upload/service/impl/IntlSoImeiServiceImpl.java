package com.mi.info.intl.retail.so.domain.upload.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.info.intl.retail.api.fieldforce.retailer.IntlRetailerApiService;
import com.mi.info.intl.retail.api.fieldforce.retailer.dto.IntlRetailerDTO;
import com.mi.info.intl.retail.api.fieldforce.user.UserApiService;
import com.mi.info.intl.retail.api.fieldforce.user.dto.IntlRmsUserNewDto;
import com.mi.info.intl.retail.api.front.dto.RmsPositionInfoRes;
import com.mi.info.intl.retail.api.front.dto.RmsStoreInfoDto;
import com.mi.info.intl.retail.api.front.position.IntlPositionApiService;
import com.mi.info.intl.retail.api.front.position.dto.IntlPositionDTO;
import com.mi.info.intl.retail.api.front.store.RmsStoreService;
import com.mi.info.intl.retail.api.so.upload.IntlSoImeiApiService;
import com.mi.info.intl.retail.component.ComponentLocator;
import com.mi.info.intl.retail.so.app.mq.SyncSoToEsProducer;
import com.mi.info.intl.retail.so.app.mq.dto.RmsSyncImeiData;
import com.mi.info.intl.retail.so.domain.datasync.dto.IntlSoImeiBatchSaveData;
import com.mi.info.intl.retail.so.domain.datasync.dto.StockDataSyncReqDto;
import com.mi.info.intl.retail.so.domain.datasync.entity.IntlDatasyncLog;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataAbnormalEnum;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataFromEnum;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncDataTypeEnum;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncOperateTypeTypeEnum;
import com.mi.info.intl.retail.so.domain.datasync.enums.SyncStatusEnum;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoImei;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoOrgInfo;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoUserInfo;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoImeiService;
import com.mi.info.intl.retail.so.infra.database.mapper.datasync.IntlDatasyncLogMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.datasync.RmsStockDataSyncMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoImeiMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoOrgInfoMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoUserInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 针对表【intl_so_imei(销量imei)】的数据库操作Service实现
 * @createDate 2025-07-25 16:36:39
 */
@Slf4j
@Service
public class IntlSoImeiServiceImpl extends ServiceImpl<IntlSoImeiMapper, IntlSoImei>
        implements IntlSoImeiService, IntlSoImeiApiService {

    @Resource
    private UserApiService userApiService;
    @Resource
    private IntlDatasyncLogMapper intlDatasyncLogMapper;
    @Resource
    private IntlSoOrgInfoMapper intlSoOrgInfoMapper;

    @Resource
    private IntlPositionApiService positionApiService;
    @Resource
    private IntlSoUserInfoMapper intlSoUserInfoMapper;
    @Resource
    private RmsStoreService rmsStoreService;
    @Resource
    private IntlRetailerApiService retailerApiService;
    @Resource
    private RmsStockDataSyncMapper rmsStockDataSyncMapper;

    @Resource
    private SyncSoToEsProducer syncSoToEsProducer;

    @Resource
    @Qualifier("syncDataThreadPool")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Override
    public IntlSoImei checkImeiExist(Long id, String rmsId) {
        return this.lambdaQuery().select(IntlSoImei::getId)
                .eq(ObjectUtils.isNotEmpty(id), IntlSoImei::getId, id)
                .eq(StringUtils.isNotEmpty(rmsId), IntlSoImei::getRmsId, rmsId).one();
    }

    @Override
    public IntlSoImei getIntlSoImeiByCondition(Long id, String rmsId) {
        return this.lambdaQuery().select()
                .eq(ObjectUtils.isNotEmpty(id), IntlSoImei::getId, id)
                .eq(StringUtils.isNotEmpty(rmsId), IntlSoImei::getRmsId, rmsId).one();
    }

    @Override
    public List<IntlSoImei> batchGetByIds(List<Long> ids) {
        return this.lambdaQuery().select()
                .in(IntlSoImei::getId, ids).list();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doImeiSave(RmsSyncImeiData data) {
        log.info("----------doImeiSave-----------，rmsId：{}", data.getRmsId());
        IntlSoImei intlSoImei = new IntlSoImei();
        Long createdByMiId = parseLongSafely(data.getCreatedbyMiid());
        Long salesmanMiId = parseLongSafely(data.getSalesManMiid());
        Long modifiedByMiId = parseLongSafely(data.getCreatedbyMiid());
        Set<Long> userMiIds = new HashSet<>();
        if (!createdByMiId.equals(0L)) {
            userMiIds.add(createdByMiId);
        }
        if (!salesmanMiId.equals(0L)) {
            userMiIds.add(salesmanMiId);
        }

        // 仅在有用户ID时才查询用户信息
        CompletableFuture<Optional<List<IntlRmsUserNewDto>>> getUserFuture =
                CompletableFuture.completedFuture(Optional.empty());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(userMiIds)) {
            getUserFuture = CompletableFuture.supplyAsync(
                    () -> userApiService.getUserListByMiIds(new ArrayList<>(userMiIds)));
        }

        // 查询门店信息
        String storeCodeRMS = data.getStoreCodeRMS();
        CompletableFuture<Optional<RmsStoreInfoDto>> storeInfoFuture =
                CompletableFuture.completedFuture(Optional.empty());
        if (StringUtils.isNotEmpty(storeCodeRMS)) {
            storeInfoFuture = CompletableFuture.supplyAsync(
                    () -> rmsStoreService.getStoreInfoByStoreCode(storeCodeRMS),
                    threadPoolTaskExecutor.getThreadPoolExecutor());
        }

        // 查询阵地信息
        String positionCodeRMS = data.getPositionCodeRMS();
        CompletableFuture<Optional<RmsPositionInfoRes>> positionInfoFuture =
                CompletableFuture.completedFuture(Optional.empty());
        if (StringUtils.isNotEmpty(positionCodeRMS)) {
            positionInfoFuture = CompletableFuture.supplyAsync(
                    () -> rmsStoreService.getPositionIfoByPositionCode(positionCodeRMS),
                    threadPoolTaskExecutor.getThreadPoolExecutor());
        }

        // 查询零售商信息
        String retailerCode = data.getRetailerCode();
        CompletableFuture<Optional<IntlRetailerDTO>> retailerFuture =
                CompletableFuture.completedFuture(Optional.empty());
        if (StringUtils.isNotEmpty(retailerCode)) {
            retailerFuture = CompletableFuture.supplyAsync(
                    () -> retailerApiService.getRetailerByRetailerCode(
                            new IntlPositionDTO().setRetailerCode(retailerCode)),
                    threadPoolTaskExecutor.getThreadPoolExecutor());
        }

        // 等待所有异步任务完成
        CompletableFuture.allOf(getUserFuture, storeInfoFuture, positionInfoFuture, retailerFuture).join();
        List<String> abnormalList = new ArrayList<>();

        // 主数据
        ComponentLocator.getConverter().convert(data, intlSoImei);

        // 处理用户信息
        IntlSoUserInfo intlSoUserInfo = handleUserInfo(getUserFuture.join(), createdByMiId, salesmanMiId, abnormalList);
        intlSoUserInfoMapper.insert(intlSoUserInfo);

        // 处理门店信息
        IntlSoOrgInfo intlSoOrgInfo =
                handleOrgInfo(storeInfoFuture.join(), positionInfoFuture.join(), retailerFuture.join(), data,
                        abnormalList);
        intlSoOrgInfoMapper.insert(intlSoOrgInfo);

        intlSoImei.setUserInfoId(intlSoUserInfo.getId());
        intlSoImei.setOrgInfoId(intlSoOrgInfo.getId());
        // 字段名不一致，手动设值
        intlSoImei.setStoreRmsCode(storeCodeRMS);
        intlSoImei.setPositionRmsCode(positionCodeRMS);
        intlSoImei.setRrp(data.getRrp());
        intlSoImei.setRrpCode(data.getRrpCode());
        intlSoImei.setVerificationResult(data.getVerifyResult());
        intlSoImei.setCreatedOn(data.getCreatedTime());
        intlSoImei.setCreatedBy(createdByMiId);
        intlSoImei.setModifiedBy(modifiedByMiId);
        intlSoImei.setModifiedOn(data.getModifiedon());
        intlSoImei.setSalesmanMid(salesmanMiId);
        intlSoImei.setDataFrom(DataFromEnum.RMS.getCode());
        intlSoImei.setReportingType(data.getReportType());
        intlSoImei.setRepeatUser(data.getRepeatUserDetail());
        baseMapper.insert(intlSoImei);
        data.setId(intlSoImei.getId());
        // 记录同步日志表
        recordSyncLog(data, DataSyncOperateTypeTypeEnum.CREATE, intlSoImei.getId(), abnormalList);

        //存量数据同步，修改同步状态为成功
        if (ObjectUtil.isNotNull(data.getIsStockData()) && data.getIsStockData() == 1) {
            StockDataSyncReqDto stockDataSyncReqDto =
                    new StockDataSyncReqDto().setSyncStatus(SyncStatusEnum.SUCCESS.getCode())
                            .setSyncEndTime(LocalDateTime.now())
                            .setIdList(Collections.singletonList(data.getIdNew()));
            rmsStockDataSyncMapper.updateImeiSyncStatus(stockDataSyncReqDto);
        }
    }

    private IntlSoUserInfo handleUserInfo(Optional<List<IntlRmsUserNewDto>> userListOptional, Long createdByMiId,
                                          Long salesmanMiId, List<String> abnormalList) {
        IntlSoUserInfo intlSoUserInfo = new IntlSoUserInfo();
        intlSoUserInfo.setCreatedByMid(createdByMiId);
        intlSoUserInfo.setSalesmanMid(salesmanMiId);
        if (!userListOptional.isPresent()) {
            abnormalList.add("未查询到上报人员用户信息，上报人员米id：" + createdByMiId);
            abnormalList.add("未查询到销售人员用户信息，销售人员米id：" + salesmanMiId);
            return intlSoUserInfo;
        }
        List<IntlRmsUserNewDto> intlRmsUserNewDtos = userListOptional.get();
        // 保证重复时不报错
        Map<Long, IntlRmsUserNewDto> userGroupByMid =
                intlRmsUserNewDtos.stream()
                        .collect(Collectors.toMap(
                                IntlRmsUserNewDto::getMiId,
                                e -> e,
                                (oldValue, newValue) -> oldValue
                        ));
        if (userGroupByMid.containsKey(createdByMiId)) {
            IntlRmsUserNewDto createUserUserInfo = userGroupByMid.get(createdByMiId);
            intlSoUserInfo.setCreatedByRmsAccount(ObjectUtils.defaultIfNull(createUserUserInfo.getDomainName(), ""));
            intlSoUserInfo.setCreatedByJobTitle(ObjectUtils.defaultIfNull(createUserUserInfo.getJobId(), 0));
        } else {
            abnormalList.add("未查询到上报人员用户信息，上报人员米id：" + createdByMiId);
        }
        if (userGroupByMid.containsKey(salesmanMiId)) {
            IntlRmsUserNewDto salesyUserInfo = userGroupByMid.get(salesmanMiId);
            intlSoUserInfo.setSalesmanRmsAccount(ObjectUtils.defaultIfNull(salesyUserInfo.getDomainName(), ""));
            intlSoUserInfo.setSalesmanJobTitle(ObjectUtils.defaultIfNull(salesyUserInfo.getJobId(), 0));
        } else {
            abnormalList.add("未查询到销售人员用户信息，销售人员米id：" + salesmanMiId);
        }
        return intlSoUserInfo;
    }

    private IntlSoOrgInfo handleOrgInfo(Optional<RmsStoreInfoDto> storeInfoOpt,
                                        Optional<RmsPositionInfoRes> positionInfoOpt,
                                        Optional<IntlRetailerDTO> retailerOpt, RmsSyncImeiData data,
                                        List<String> abnormalList) {
        IntlSoOrgInfo intlSoOrgInfo = new IntlSoOrgInfo();
        intlSoOrgInfo.setStoreCode(ObjectUtils.defaultIfNull(data.getStoreCodeNew(), ""));
        intlSoOrgInfo.setStoreRmsCode(ObjectUtils.defaultIfNull(data.getStoreCodeRMS(), ""));
        intlSoOrgInfo.setPositionRmsCode(ObjectUtils.defaultIfNull(data.getPositionCodeRMS(), ""));
        intlSoOrgInfo.setPositionCode(ObjectUtils.defaultIfNull(data.getPositionCodeNew(), ""));
        intlSoOrgInfo.setRetailerCode(ObjectUtils.defaultIfNull(data.getRetailerCode(), ""));

        if (storeInfoOpt.isPresent()) {
            RmsStoreInfoDto rmsStoreInfoDto = storeInfoOpt.get();
            intlSoOrgInfo.setStoreType(ObjectUtils.defaultIfNull(rmsStoreInfoDto.getType(), 0));
            intlSoOrgInfo.setStoreGrade(ObjectUtils.defaultIfNull(rmsStoreInfoDto.getGrade(), 0));
            intlSoOrgInfo.setStoreHasPC(ObjectUtils.defaultIfNull(rmsStoreInfoDto.getHasPc(), 0));
            intlSoOrgInfo.setStoreHasSR(ObjectUtils.defaultIfNull(rmsStoreInfoDto.getHasSR(), 0));
            intlSoOrgInfo.setStoreChannelType(ObjectUtils.defaultIfNull(rmsStoreInfoDto.getChannelType(), 0));
            intlSoOrgInfo.setCountryCode(ObjectUtils.defaultIfNull(rmsStoreInfoDto.getCountryShortcode(), ""));
            intlSoOrgInfo.setStoreId(ObjectUtils.defaultIfNull(rmsStoreInfoDto.getId(), 0));
        } else {
            abnormalList.add("未查询到门店信息，storeCodeRms：" + data.getStoreCodeRMS());
        }

        if (positionInfoOpt.isPresent()) {
            RmsPositionInfoRes rmsPositionInfoDto = positionInfoOpt.get();
            intlSoOrgInfo.setPositionType(ObjectUtils.defaultIfNull(rmsPositionInfoDto.getType(), 0));
            intlSoOrgInfo.setPositionId(ObjectUtils.defaultIfNull(rmsPositionInfoDto.getId(), 0));
        } else {
            abnormalList.add("未查询到阵地信息，positionCodeRms：" + data.getPositionCodeRMS());
        }

        if (retailerOpt.isPresent()) {
            IntlRetailerDTO intlRetailerDTO = retailerOpt.get();
            intlSoOrgInfo.setRetailerId(ObjectUtils.defaultIfNull(intlRetailerDTO.getId(), 0L));
        } else {
            abnormalList.add("未查询到零售商信息，retailerCode：" + data.getRetailerCode());
        }

        return intlSoOrgInfo;
    }

    private Long parseLongSafely(String str) {
        if (StringUtils.isEmpty(str)) {
            return 0L;
        }
        try {
            return NumberUtils.createLong(str);
        } catch (NumberFormatException e) {
            log.warn("Failed to parse Long value: {}", str);
            return 0L;
        }
    }

    private void recordSyncLog(RmsSyncImeiData data, DataSyncOperateTypeTypeEnum operateType, Long intlSoImeiId,
                               List<String> abnormalList) {
        IntlDatasyncLog intlDatasyncLog = new IntlDatasyncLog()
                .setType(DataSyncDataTypeEnum.IMEI.getCode())
                .setRmsId(data.getRmsId())
                .setRetailId(intlSoImeiId)
                .setMessage(JSON.toJSONString(data))
                .setOperateType(operateType.getCode());
        if (CollectionUtils.isNotEmpty(abnormalList)) {
            intlDatasyncLog.setIsDataAbnormal(DataAbnormalEnum.DATA_ABNORMAL.getCode());
            intlDatasyncLog.setAbnormalMessage(String.join(";", abnormalList));
        }
        intlDatasyncLogMapper.insert(intlDatasyncLog);
    }

    /**
     * @param operatorType 修改类型
     * @param fields 修改的字段集合
     * @param data 修改的数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doImeiUpdate(String operatorType, List<String> fields, RmsSyncImeiData data) {
        log.info("----------doImeiUpdate-----------，rmsId：{}，retailId：{}", data.getRmsId(), data.getRetailId());
        IntlSoImei intlSoImeiByCondition = getIntlSoImeiByCondition(data.getId(), data.getRmsId());
        if (intlSoImeiByCondition == null) {
            log.info("doImeiUpdate intlSoImei 数据不存在, rmsId: {},retailId：{}， timestamp: {}", data.getRmsId(),
                    data.getRetailId(),
                    System.currentTimeMillis());
            return;
        }

        LambdaUpdateWrapper<IntlSoImei> wrapper = Wrappers.lambdaUpdate(IntlSoImei.class)
                .eq(IntlSoImei::getId, data.getId())
                // 使用修改时间做乐观锁，同步的数据修改时间必须大于数据库中修改时间才做修改
                .lt(IntlSoImei::getModifiedOn, data.getModifiedon());

        wrapper.set(IntlSoImei::getImeiRuleIsActivingCheck, data.getImeiRuleIsActivingCheck())
                .set(IntlSoImei::getImeiRuleBefore, data.getImeiRuleBefore())
                .set(IntlSoImei::getImeiRuleAfter, data.getImeiRuleAfter())
                .set(IntlSoImei::getActivationVerificationTime, data.getActivationVerificationTime())
                .set(IntlSoImei::getVerifyingState, data.getVerifyingState())
                .set(IntlSoImei::getActivationTime, data.getActivationTime())
                .set(IntlSoImei::getActivationFrequency, data.getActivationFrequency())
                .set(IntlSoImei::getActivationSite, data.getActivationSite())
                .set(IntlSoImei::getSiVerifyResult, data.getSiVerifyResult())
                .set(IntlSoImei::getVerifyResultDetail, data.getVerifyResultDetail())
                .set(IntlSoImei::getVerificationResult, data.getVerifyResult())
                .set(IntlSoImei::getRepeatUser, data.getRepeatUserDetail())
                .set(IntlSoImei::getFinalSalesCountry, data.getFinalSalesCountry())
                .set(IntlSoImei::getFailedReason, data.getFailedReason())
                .set(IntlSoImei::getFailedReasonDetail, data.getFailedReasonDetail())
                .set(IntlSoImei::getFirstLevelAccountCode, data.getFirstLevelAccountCode())
                .set(IntlSoImei::getModifiedOn, data.getModifiedon())
                .set(IntlSoImei::getModifiedBy, parseLongSafely(data.getModifiedbyMiId()));
        baseMapper.update(null, wrapper);

        // 记录同步日志表
        recordSyncLog(data, DataSyncOperateTypeTypeEnum.UPDATE, intlSoImeiByCondition.getId(), null);
    }

    @Override
    public List<IntlSoImei> getNeedVerifyImeiList(Integer verifyState, Integer verifyResult, Integer limit) {
        if (null == verifyState || null == verifyResult || null == limit || limit <= 0) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<IntlSoImei> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlSoImei::getVerifyingState, verifyState);
        queryWrapper.eq(IntlSoImei::getVerificationResult, verifyResult);
        queryWrapper.ne(IntlSoImei::getSnHash, "");
        queryWrapper.ne(IntlSoImei::getSalesTime, 0L);
        queryWrapper.orderByAsc(IntlSoImei::getActivationVerificationTime).last("limit " + limit);
        return baseMapper.selectList(queryWrapper);
    }

    @Override
    public int batchSoftUpdateById(List<IntlSoImei> intlSoImeiList) {
        if (CollectionUtils.isEmpty(intlSoImeiList)) {
            return 0;
        }
        return baseMapper.batchUpdateById(intlSoImeiList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doImeiBatchSave(List<RmsSyncImeiData> imeiDataList) {
        log.info("----------doImeiBatchSave-----------, dataSize: {}", imeiDataList.size());
        if (CollectionUtils.isEmpty(imeiDataList)) {
            log.warn("doImeiBatchSave imeiDataList is empty");
            return;
        }

        // 1. 过滤掉已存在的数据
        List<RmsSyncImeiData> filteredDataList = filterExistingData(imeiDataList);
        if (filteredDataList.isEmpty()) {
            log.info("All data already exists, no need to insert");
            return;
        }
        //组装需要的门店、阵地等数据
        BatchDependencyData batchDependencyData = fetchBatchDependencyData(filteredDataList);

        // 构建并插入数据
        List<IntlSoImeiBatchSaveData> imeiEntities = buildBatchInsertData(filteredDataList, batchDependencyData);

        executeBatchInsert(imeiEntities);
        log.info("Batch saved {} IMEI records", imeiEntities.size());
    }


    /**
     * 过滤掉已存在的数据
     */
    private List<RmsSyncImeiData> filterExistingData(List<RmsSyncImeiData> imeiDataList) {
        List<String> rmsIds = imeiDataList.stream()
                .map(RmsSyncImeiData::getRmsId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (rmsIds.isEmpty()) {
            return imeiDataList;
        }

        List<IntlSoImei> existingImeis = this.lambdaQuery()
                .in(IntlSoImei::getRmsId, rmsIds)
                .list();

        Set<String> existingRmsIds = existingImeis.stream()
                .map(IntlSoImei::getRmsId)
                .collect(Collectors.toSet());

        return imeiDataList.stream()
                .filter(data -> !existingRmsIds.contains(data.getRmsId()))
                .collect(Collectors.toList());
    }

    /**
     * 批量获取依赖数据
     */
    private BatchDependencyData fetchBatchDependencyData(List<RmsSyncImeiData> imeiDataList) {
        BatchDependencyData dependencyData = new BatchDependencyData();

        // 获取用户信息
        dependencyData.userListOptional = fetchUserList(imeiDataList);

        // 获取门店、阵地、零售商信息
        Map<String, Set<String>> codes = extractCodes(imeiDataList);
        dependencyData.storeInfoMap = fetchStoreInfo(codes.get("storeCodes"));
        dependencyData.positionInfoMap = fetchPositionInfo(codes.get("positionCodes"));
        dependencyData.retailerInfoMap = fetchRetailerInfo(codes.get("retailerCodes"));

        return dependencyData;
    }

    /**
     * 获取用户列表
     */
    private Optional<List<IntlRmsUserNewDto>> fetchUserList(List<RmsSyncImeiData> imeiDataList) {
        Set<Long> userMiIds = new HashSet<>();
        for (RmsSyncImeiData data : imeiDataList) {
            Long createdByMiId = parseLongSafely(data.getCreatedbyMiid());
            Long salesmanMiId = parseLongSafely(data.getSalesManMiid());
            if (!createdByMiId.equals(0L)) {
                userMiIds.add(createdByMiId);
            }
            if (!salesmanMiId.equals(0L)) {
                userMiIds.add(salesmanMiId);
            }
        }

        if (userMiIds.isEmpty()) {
            return Optional.empty();
        }

        return userApiService.getUserListByMiIds(new ArrayList<>(userMiIds));
    }

    /**
     * 提取各种代码
     */
    private Map<String, Set<String>> extractCodes(List<RmsSyncImeiData> imeiDataList) {
        Set<String> storeCodes = imeiDataList.stream()
                .map(RmsSyncImeiData::getStoreCodeRMS)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toSet());

        Set<String> positionCodes = imeiDataList.stream()
                .map(RmsSyncImeiData::getPositionCodeRMS)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toSet());

        Set<String> retailerCodes = imeiDataList.stream()
                .map(RmsSyncImeiData::getRetailerCode)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toSet());

        Map<String, Set<String>> codes = new HashMap<>();
        codes.put("storeCodes", storeCodes);
        codes.put("positionCodes", positionCodes);
        codes.put("retailerCodes", retailerCodes);
        return codes;
    }

    /**
     * 获取门店信息
     */
    private Map<String, RmsStoreInfoDto> fetchStoreInfo(Set<String> storeCodes) {
        Map<String, RmsStoreInfoDto> storeInfoMap = new HashMap<>();
        if (storeCodes.isEmpty()) {
            return storeInfoMap;
        }

        try {
            Map<String, RmsStoreInfoDto> batchStoreInfo =
                    rmsStoreService.batchGetStoreInfoByStoreCode(new ArrayList<>(storeCodes));
            storeInfoMap.putAll(batchStoreInfo);
        } catch (Exception e) {
            log.warn("Batch query store info failed, fallback to individual queries", e);
            // 降级到逐个查询
            for (String storeCode : storeCodes) {
                try {
                    Optional<RmsStoreInfoDto> storeInfo = rmsStoreService.getStoreInfoByStoreCode(storeCode);
                    storeInfo.ifPresent(info -> storeInfoMap.put(storeCode, info));
                } catch (Exception ex) {
                    log.warn("Query store info failed for storeCode: {}", storeCode, ex);
                }
            }
        }
        return storeInfoMap;
    }

    /**
     * 获取阵地信息
     */
    private Map<String, RmsPositionInfoRes> fetchPositionInfo(Set<String> positionCodes) {
        Map<String, RmsPositionInfoRes> positionInfoMap = new HashMap<>();
        if (positionCodes.isEmpty()) {
            return positionInfoMap;
        }

        try {
            Map<String, RmsPositionInfoRes> batchPositionInfo =
                    positionApiService.getPositionInfoByPositionCodes(new ArrayList<>(positionCodes));
            positionInfoMap.putAll(batchPositionInfo);
        } catch (Exception e) {
            log.warn("Batch query position info failed, fallback to individual queries", e);
            // 降级到逐个查询
            for (String positionCode : positionCodes) {
                try {
                    Optional<RmsPositionInfoRes> positionInfo =
                            rmsStoreService.getPositionIfoByPositionCode(positionCode);
                    positionInfo.ifPresent(info -> positionInfoMap.put(positionCode, info));
                } catch (Exception ex) {
                    log.warn("Query position info failed for positionCode: {}", positionCode, ex);
                }
            }
        }
        return positionInfoMap;
    }

    /**
     * 获取零售商信息
     */
    private Map<String, IntlRetailerDTO> fetchRetailerInfo(Set<String> retailerCodes) {
        Map<String, IntlRetailerDTO> retailerInfoMap = new HashMap<>();
        if (retailerCodes.isEmpty()) {
            return retailerInfoMap;
        }

        try {
            Map<String, IntlRetailerDTO> batchRetailerInfo =
                    retailerApiService.getRetailersByRetailerCodes(new ArrayList<>(retailerCodes));
            retailerInfoMap.putAll(batchRetailerInfo);
        } catch (Exception e) {
            log.warn("Batch query retailer info failed, fallback to individual queries", e);
            // 降级到逐个查询
            for (String retailerCode : retailerCodes) {
                try {
                    Optional<IntlRetailerDTO> retailerInfo = retailerApiService.getRetailerByRetailerCode(
                            new IntlPositionDTO().setRetailerCode(retailerCode));
                    retailerInfo.ifPresent(info -> retailerInfoMap.put(retailerCode, info));
                } catch (Exception ex) {
                    log.warn("Query retailer info failed for retailerCode: {}", retailerCode, ex);
                }
            }
        }
        return retailerInfoMap;
    }

    /**
     * 构建批量插入数据
     */
    private List<IntlSoImeiBatchSaveData> buildBatchInsertData(List<RmsSyncImeiData> imeiDataList,
                                                               BatchDependencyData dependencyData) {
        List<IntlSoImeiBatchSaveData> imeiEntities = new ArrayList<>();

        for (RmsSyncImeiData data : imeiDataList) {
            // 构建用户信息
            Long createdByMiId = parseLongSafely(data.getCreatedbyMiid());
            Long salesmanMiId = parseLongSafely(data.getSalesManMiid());
            List<String> abnormalList = new ArrayList<>();
            IntlSoUserInfo userInfo =
                    handleUserInfo(dependencyData.userListOptional, createdByMiId, salesmanMiId, abnormalList);
            intlSoUserInfoMapper.insert(userInfo);
            // 构建组织信息
            IntlSoOrgInfo orgInfo = handleOrgInfo(
                    Optional.ofNullable(dependencyData.storeInfoMap.get(data.getStoreCodeRMS())),
                    Optional.ofNullable(dependencyData.positionInfoMap.get(data.getPositionCodeRMS())),
                    Optional.ofNullable(dependencyData.retailerInfoMap.get(data.getRetailerCode())),
                    data,
                    abnormalList
            );
            intlSoOrgInfoMapper.insert(orgInfo);
            // 构建IMEI实体
            IntlSoImeiBatchSaveData imeiEntity = new IntlSoImeiBatchSaveData();
            ComponentLocator.getConverter().convert(data, imeiEntity);

            imeiEntity.setUserInfoId(userInfo.getId());
            imeiEntity.setOrgInfoId(orgInfo.getId());
            imeiEntity.setStoreRmsCode(data.getStoreCodeRMS());
            imeiEntity.setPositionRmsCode(data.getPositionCodeRMS());
            imeiEntity.setRrp(data.getRrp());
            imeiEntity.setRrpCode(data.getRrpCode());
            imeiEntity.setVerificationResult(data.getVerifyResult());
            imeiEntity.setCreatedOn(data.getCreatedTime());
            imeiEntity.setCreatedBy(createdByMiId);
            imeiEntity.setModifiedBy(createdByMiId);
            imeiEntity.setModifiedOn(data.getModifiedOn());
            imeiEntity.setSalesmanMid(salesmanMiId);
            imeiEntity.setDataFrom(DataFromEnum.RMS.getCode());
            imeiEntity.setReportingType(data.getReportType());
            imeiEntity.setRepeatUser(data.getRepeatUserDetail());
            imeiEntity.setAbnormalList(abnormalList);
            imeiEntity.setIsStockData(data.getIsStockData());
            imeiEntity.setStockId(data.getIdNew());
            imeiEntities.add(imeiEntity);
        }

        return imeiEntities;
    }

    /**
     * 执行批量插入
     */
    private void executeBatchInsert(List<IntlSoImeiBatchSaveData> imeiEntities) {

        // 批量插入IMEI数据
        if (!imeiEntities.isEmpty()) {
            baseMapper.batchInsert(imeiEntities);
        }

        List<IntlDatasyncLog> syncLogs = new ArrayList<>();

        List<Long> stockIdList = new ArrayList<>();
        // 构建同步日志
        imeiEntities.forEach(imeiEntity -> {
            IntlDatasyncLog syncLog = new IntlDatasyncLog()
                    .setType(DataSyncDataTypeEnum.IMEI.getCode())
                    .setRmsId(imeiEntity.getRmsId())
                    .setRetailId(imeiEntity.getId())
                    .setMessage(JSON.toJSONString(imeiEntity))
                    .setOperateType(DataSyncOperateTypeTypeEnum.CREATE.getCode());
            syncLogs.add(syncLog);

            List<String> abnormalList = imeiEntity.getAbnormalList();
            if (CollectionUtils.isNotEmpty(abnormalList)) {
                syncLog.setIsDataAbnormal(DataAbnormalEnum.DATA_ABNORMAL.getCode());
                syncLog.setAbnormalMessage(String.join(";", abnormalList));
            }
            if (ObjectUtil.isNotNull(imeiEntity.getIsStockData()) && imeiEntity.getIsStockData() == 1) {
                stockIdList.add(imeiEntity.getStockId());
            }

        });

        // 批量插入同步日志
        intlDatasyncLogMapper.batchInsert(syncLogs);

        //存量数据同步，修改同步状态为成功
        if (CollectionUtil.isNotEmpty(stockIdList)) {
            StockDataSyncReqDto stockDataSyncReqDto =
                    new StockDataSyncReqDto().setSyncStatus(SyncStatusEnum.SUCCESS.getCode())
                            .setSyncEndTime(LocalDateTime.now())
                            .setIdList(stockIdList);
            rmsStockDataSyncMapper.updateImeiSyncStatus(stockDataSyncReqDto);
        }
    }

    /**
     * 批量依赖数据类
     */
    private static class BatchDependencyData {
        Optional<List<IntlRmsUserNewDto>> userListOptional = Optional.empty();
        Map<String, RmsStoreInfoDto> storeInfoMap = new HashMap<>();
        Map<String, RmsPositionInfoRes> positionInfoMap = new HashMap<>();
        Map<String, IntlRetailerDTO> retailerInfoMap = new HashMap<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doImeiBatchUpdate(String operateType, List<RmsSyncImeiData> imeiDataList) {
        log.info("----------doImeiBatchUpdate-----------, operateType: {}, dataSize: {}", operateType,
                imeiDataList.size());
        if (CollectionUtils.isEmpty(imeiDataList)) {
            log.warn("doImeiBatchUpdate imeiDataList is empty");
            return;
        }

        // 1. 批量查询现有数据
        List<String> rmsIds = imeiDataList.stream()
                .map(RmsSyncImeiData::getRmsId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (rmsIds.isEmpty()) {
            log.warn("No valid rmsIds found for batch update");
            return;
        }

        List<IntlSoImei> existingImeis = this.lambdaQuery()
                .in(IntlSoImei::getRmsId, rmsIds)
                .select(IntlSoImei::getId, IntlSoImei::getRmsId)
                .list();

        Map<String, IntlSoImei> existingImeiMap = existingImeis.stream()
                .collect(Collectors.toMap(IntlSoImei::getRmsId, i -> i));

        // 2. 构建批量更新数据
        List<IntlSoImei> updateImeis = new ArrayList<>();
        List<IntlDatasyncLog> syncLogs = new ArrayList<>();

        for (RmsSyncImeiData data : imeiDataList) {
            IntlSoImei existingImei = existingImeiMap.get(data.getRmsId());
            if (existingImei == null) {
                log.warn("IMEI data not found for rmsId: {}", data.getRmsId());
                continue;
            }

            // 只有当修改时间大于现有时间时才更新（乐观锁）
            if (existingImei.getModifiedOn() != null &&
                    existingImei.getModifiedOn().compareTo(data.getModifiedon()) >= 0) {
                continue;
            }

            // 更新字段
            existingImei.setImeiRuleIsActivingCheck(data.getImeiRuleIsActivingCheck())
                    .setImeiRuleBefore(data.getImeiRuleBefore())
                    .setImeiRuleAfter(data.getImeiRuleAfter())
                    .setActivationVerificationTime(data.getActivationVerificationTime())
                    .setVerifyingState(data.getVerifyingState())
                    .setActivationTime(data.getActivationTime())
                    .setActivationFrequency(data.getActivationFrequency())
                    .setActivationSite(data.getActivationSite())
                    .setSiVerifyResult(data.getSiVerifyResult())
                    .setVerifyResultDetail(data.getVerifyResultDetail())
                    .setVerificationResult(data.getVerifyResult())
                    .setRepeatUser(data.getRepeatUserDetail())
                    .setFinalSalesCountry(data.getFinalSalesCountry())
                    .setFailedReason(data.getFailedReason())
                    .setFailedReasonDetail(data.getFailedReasonDetail())
                    .setFirstLevelAccountCode(data.getFirstLevelAccountCode())
                    .setModifiedOn(data.getModifiedon())
                    .setModifiedBy(parseLongSafely(data.getModifiedbyMiId()));

            updateImeis.add(existingImei);

            // 构建同步日志
            IntlDatasyncLog syncLog = new IntlDatasyncLog()
                    .setType(DataSyncDataTypeEnum.IMEI.getCode())
                    .setRmsId(data.getRmsId())
                    .setRetailId(existingImei.getId())
                    .setMessage(JSON.toJSONString(data))
                    .setOperateType(DataSyncOperateTypeTypeEnum.UPDATE.getCode());

            syncLogs.add(syncLog);
        }

        // 3. 批量更新（使用自定义SQL）
        if (!updateImeis.isEmpty()) {
            baseMapper.batchUpdate(updateImeis);
        }

        if (!syncLogs.isEmpty()) {
            intlDatasyncLogMapper.batchInsert(syncLogs);
        }

        List<Long> stockIdList =
                imeiDataList.stream()
                        .filter(imeiEntity -> ObjectUtil.isNotNull(imeiEntity.getIsStockData()) &&
                                imeiEntity.getIsStockData() == 1).map(RmsSyncImeiData::getIdNew)
                        .collect(Collectors.toList());

        //存量数据同步，修改同步状态为成功
        if (CollectionUtil.isNotEmpty(stockIdList)) {
            StockDataSyncReqDto stockDataSyncReqDto =
                    new StockDataSyncReqDto().setSyncStatus(SyncStatusEnum.SUCCESS.getCode())
                            .setSyncEndTime(LocalDateTime.now())
                            .setIdList(stockIdList);
            rmsStockDataSyncMapper.updateImeiSyncStatus(stockDataSyncReqDto);
            log.info("Batch updated {} IMEI records", updateImeis.size());
        }
    }

    public List<IntlSoImei> batchGetImeiByRmsId(List<Long> retailIds, List<String> rmsIds) {
        if (CollectionUtils.isNotEmpty(retailIds)) {
            return baseMapper.selectByRetailerIds(retailIds);
        } else if (CollectionUtils.isNotEmpty(rmsIds)) {
            return baseMapper.selectByRmsIds(rmsIds);
        }
        return new ArrayList<>();
    }


    @Override
    public int updateIsPhotoExistByDetailIds(List<String> detailIds) {
        if (CollectionUtils.isEmpty(detailIds)) {
            log.warn("updateIsPhotoExistByDetailIds: detailIds is empty");
            return 0;
        }

        log.info("updateIsPhotoExistByDetailIds: updating {} records", detailIds.size());

        LambdaUpdateWrapper<IntlSoImei> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(IntlSoImei::getIsPhotoExist, 1)
                    .in(IntlSoImei::getDetailId, detailIds);

        int updateCount = baseMapper.update(null, updateWrapper);
        log.info("updateIsPhotoExistByDetailIds: updated {} records", updateCount);

        return updateCount;
    }
}
