package com.mi.info.intl.retail.so.domain.upload.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.info.intl.retail.api.fieldforce.retailer.IntlRetailerApiService;
import com.mi.info.intl.retail.api.fieldforce.retailer.dto.IntlRetailerDTO;
import com.mi.info.intl.retail.api.fieldforce.user.UserApiService;
import com.mi.info.intl.retail.api.fieldforce.user.dto.IntlRmsUserNewDto;
import com.mi.info.intl.retail.api.front.dto.RmsPositionInfoRes;
import com.mi.info.intl.retail.api.front.dto.RmsStoreInfoDto;
import com.mi.info.intl.retail.api.front.position.IntlPositionApiService;
import com.mi.info.intl.retail.api.front.position.dto.IntlPositionDTO;
import com.mi.info.intl.retail.api.front.store.RmsStoreService;
import com.mi.info.intl.retail.api.so.upload.IntlSoQtyApiService;
import com.mi.info.intl.retail.component.ComponentLocator;
import com.mi.info.intl.retail.so.app.mq.dto.RmsSyncQtyData;
import com.mi.info.intl.retail.so.domain.datasync.dto.IntlSoQtyBatchSaveData;
import com.mi.info.intl.retail.so.domain.datasync.dto.StockDataSyncReqDto;
import com.mi.info.intl.retail.so.domain.datasync.entity.IntlDatasyncLog;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataAbnormalEnum;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataFromEnum;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncDataTypeEnum;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncOperateTypeTypeEnum;
import com.mi.info.intl.retail.so.domain.datasync.enums.SyncStatusEnum;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoOrgInfo;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoQty;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoUserInfo;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoQtyService;
import com.mi.info.intl.retail.so.infra.database.mapper.datasync.IntlDatasyncLogMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.datasync.RmsStockDataSyncMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoOrgInfoMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoQtyMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoUserInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 针对表【intl_so_qty(销量qty)】的数据库操作Service实现
 * @createDate 2025-07-24 19:21:34
 */
@Slf4j
@Service
public class IntlSoQtyServiceImpl extends ServiceImpl<IntlSoQtyMapper, IntlSoQty>
        implements IntlSoQtyService, IntlSoQtyApiService {

    @Resource
    private UserApiService userApiService;
    @Resource
    private IntlDatasyncLogMapper intlDatasyncLogMapper;
    @Resource
    private IntlSoOrgInfoMapper intlSoOrgInfoMapper;
    @Resource
    private IntlSoUserInfoMapper intlSoUserInfoMapper;
    @Resource
    private RmsStoreService rmsStoreService;

    @Resource
    private IntlPositionApiService positionApiService;
    @Resource
    private IntlRetailerApiService retailerApiService;
    @Resource
    @Qualifier("syncDataThreadPool")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Resource
    private RmsStockDataSyncMapper rmsStockDataSyncMapper;

    @Override
    public IntlSoQty getIntlSoQtyByRmsId(String rmsId) {
        return this.lambdaQuery().eq(IntlSoQty::getRmsId, rmsId)
                .select(IntlSoQty::getId).one();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doQtySave(RmsSyncQtyData data) {
        log.info("------------doQtySave------------,rmsId:{}", data.getRmsId());
        String rmsId = data.getRmsId();
        IntlSoQty intlSoQtyByRmsId = getIntlSoQtyByRmsId(rmsId);
        if (intlSoQtyByRmsId != null) {
            log.info("doQtySave qty 已存在, rmsId: {}, timestamp: {}", rmsId, System.currentTimeMillis());
            return;
        }

        IntlSoQty intlSoQty = new IntlSoQty();
        String createdbyMiidStr = data.getCreatedbyMiid();
        String salesManMiidStr = data.getSalesmanMiid();
        Long modifiedByMiId = parseLong(data.getCreatedbyMiid());
        Long createdByMiId = parseLong(createdbyMiidStr);
        Long salesmanMiId = parseLong(salesManMiidStr);
        Set<Long> userMiIds = new HashSet<>();
        if (!createdByMiId.equals(0L)) {
            userMiIds.add(createdByMiId);
        }
        if (!salesmanMiId.equals(0L)) {
            userMiIds.add(salesmanMiId);
        }

        // 仅在有用户ID时才查询用户信息
        CompletableFuture<Optional<List<IntlRmsUserNewDto>>> getUserFuture =
                CompletableFuture.completedFuture(Optional.empty());
        if (CollectionUtils.isNotEmpty(userMiIds)) {
            getUserFuture = CompletableFuture.supplyAsync(
                    () -> userApiService.getUserListByMiIds(new ArrayList<>(userMiIds)));
        }

        // 查询门店信息
        String storeCodeRMS = data.getStoreCodeRMS();
        CompletableFuture<Optional<RmsStoreInfoDto>> storeInfoFuture =
                CompletableFuture.completedFuture(Optional.empty());
        if (StringUtils.isNotEmpty(storeCodeRMS)) {
            storeInfoFuture = CompletableFuture.supplyAsync(
                    () -> rmsStoreService.getStoreInfoByStoreCode(storeCodeRMS),
                    threadPoolTaskExecutor.getThreadPoolExecutor());
        }

        // 查询阵地信息
        String positionCodeRMS = data.getPositionCodeRMS();
        CompletableFuture<Optional<RmsPositionInfoRes>> positionInfoFuture =
                CompletableFuture.completedFuture(Optional.empty());
        if (StringUtils.isNotEmpty(positionCodeRMS)) {
            positionInfoFuture = CompletableFuture.supplyAsync(
                    () -> rmsStoreService.getPositionIfoByPositionCode(positionCodeRMS),
                    threadPoolTaskExecutor.getThreadPoolExecutor());
        }

        // 查询零售商信息
        String retailerCode = data.getRetailerCode();
        CompletableFuture<Optional<IntlRetailerDTO>> retailerFuture =
                CompletableFuture.completedFuture(Optional.empty());
        if (StringUtils.isNotEmpty(retailerCode)) {
            retailerFuture = CompletableFuture.supplyAsync(
                    () -> retailerApiService.getRetailerByRetailerCode(
                            new IntlPositionDTO().setRetailerCode(retailerCode)),
                    threadPoolTaskExecutor.getThreadPoolExecutor());
        }

        // 等待所有异步任务完成
        CompletableFuture.allOf(getUserFuture, storeInfoFuture, positionInfoFuture, retailerFuture).join();
        List<String> abnormalList = new ArrayList<>();

        // 处理用户信息
        IntlSoUserInfo intlSoUserInfo = handleUserInfo(getUserFuture.join(), createdByMiId, salesmanMiId, abnormalList);
        intlSoUserInfoMapper.insert(intlSoUserInfo);

        // 处理门店信息
        IntlSoOrgInfo intlSoOrgInfo =
                handleOrgInfo(storeInfoFuture.join(), positionInfoFuture.join(),
                        retailerFuture.join(), data,
                        abnormalList);
        intlSoOrgInfoMapper.insert(intlSoOrgInfo);
        ComponentLocator.getConverter().convert(data, intlSoQty);
        intlSoQty.setReportingType(data.getReportType());
        intlSoQty.setStoreRmsCode(storeCodeRMS);
        intlSoQty.setPositionRmsCode(positionCodeRMS);
        intlSoQty.setRetailerCode(retailerCode);
        intlSoQty.setUserInfoId(intlSoUserInfo.getId());
        intlSoQty.setOrgInfoId(intlSoOrgInfo.getId());
        intlSoQty.setSalesmanMid(salesmanMiId);
        intlSoQty.setCreatedby(createdByMiId);
        intlSoQty.setModifiedby(modifiedByMiId);
        intlSoQty.setRrpCode(data.getRrpRMSCode());
        intlSoQty.setDataFrom(DataFromEnum.RMS.getCode());
        baseMapper.insert(intlSoQty);

        data.setId(intlSoQty.getId());

        // 记录同步日志表
        recordSyncLog(data, DataSyncOperateTypeTypeEnum.CREATE, intlSoQty.getId(), abnormalList);

        //存量数据同步，修改同步状态为成功
        if (ObjectUtil.isNotNull(data.getIsStockData()) && data.getIsStockData() == 1) {
            StockDataSyncReqDto stockDataSyncReqDto =
                    new StockDataSyncReqDto().setSyncStatus(SyncStatusEnum.SUCCESS.getCode())
                            .setSyncEndTime(LocalDateTime.now())
                            .setIdList(Collections.singletonList(data.getIdNew()));
            rmsStockDataSyncMapper.updateQtySyncStatus(stockDataSyncReqDto);
        }
    }

    @Override
    public IntlSoQty getById(Long id) {
        return this.lambdaQuery().eq(IntlSoQty::getId, id)
                .select().one();
    }

    @Override
    public List<IntlSoQty> getByIds(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return new ArrayList<>();
        }
        return this.lambdaQuery().in(IntlSoQty::getId, ids)
                .select().list();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doQtyUpdate(RmsSyncQtyData data) {
        log.info("------------doQtyUpdate------------,rmsId:{}", data.getRmsId());
        String rmsId = data.getRmsId();
        IntlSoQty intlSoQty = getIntlSoQtyByRmsId(data.getRmsId());
        if (intlSoQty == null) {
            log.info("doQtyUpdate qty 不存在, rmsId: {}, timestamp: {}", rmsId, System.currentTimeMillis());
            return;
        }

        LambdaUpdateWrapper<IntlSoQty> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(IntlSoQty::getStatus, data.getStatus())
                .set(IntlSoQty::getModifiedon, data.getModifiedon())
                .set(IntlSoQty::getModifiedby, parseLong(data.getModifiedbyMiId()))
                .eq(IntlSoQty::getRmsId, data.getRmsId());
        baseMapper.update(updateWrapper);

        data.setId(intlSoQty.getId());
        // 记录同步日志表
        recordSyncLog(data, DataSyncOperateTypeTypeEnum.UPDATE, intlSoQty.getId(), null);
    }

    private Long parseLong(String str) {
        if (StringUtils.isEmpty(str)) {
            return 0L;
        }
        try {
            return Long.valueOf(str);
        } catch (NumberFormatException e) {
            log.warn("Invalid number format: {}", str, e);
            return 0L;
        }
    }

    private void recordSyncLog(RmsSyncQtyData data, DataSyncOperateTypeTypeEnum operateType, Long intlSoQtyId,
                               List<String> abnormalList) {
        IntlDatasyncLog intlDatasyncLog = new IntlDatasyncLog()
                .setType(DataSyncDataTypeEnum.QTY.getCode())
                .setRmsId(data.getRmsId())
                .setRetailId(intlSoQtyId)
                .setMessage(JSON.toJSONString(data))
                .setOperateType(operateType.getCode());

        if (CollectionUtils.isNotEmpty(abnormalList)) {
            intlDatasyncLog.setIsDataAbnormal(DataAbnormalEnum.DATA_ABNORMAL.getCode());
            intlDatasyncLog.setAbnormalMessage(String.join(";", abnormalList));
        }
        intlDatasyncLogMapper.insert(intlDatasyncLog);
    }

    private IntlSoUserInfo handleUserInfo(Optional<List<IntlRmsUserNewDto>> userListOptional, Long createdByMiId,
                                          Long salesmanMiId, List<String> abnormalList) {
        IntlSoUserInfo intlSoUserInfo = new IntlSoUserInfo();
        intlSoUserInfo.setCreatedByMid(createdByMiId);
        intlSoUserInfo.setSalesmanMid(salesmanMiId);
        if (!userListOptional.isPresent()) {
            abnormalList.add("未查询到上报人员用户信息，上报人员米id：" + createdByMiId);
            abnormalList.add("未查询到销售人员用户信息，销售人员米id：" + salesmanMiId);
            return intlSoUserInfo;
        }
        List<IntlRmsUserNewDto> intlRmsUserNewDtos = userListOptional.get();
        // 保证重复时不报错
        Map<Long, IntlRmsUserNewDto> userGroupByMid =
                intlRmsUserNewDtos.stream()
                        .collect(Collectors.toMap(
                                IntlRmsUserNewDto::getMiId,
                                e -> e,
                                (oldValue, newValue) -> oldValue // 保留旧值
                        ));
        if (userGroupByMid.containsKey(createdByMiId)) {
            IntlRmsUserNewDto createUserUserInfo = userGroupByMid.get(createdByMiId);
            intlSoUserInfo.setCreatedByRmsAccount(ObjectUtils.defaultIfNull(createUserUserInfo.getDomainName(), ""));
            intlSoUserInfo.setCreatedByJobTitle(ObjectUtils.defaultIfNull(createUserUserInfo.getJobId(), 0));
        } else {
            abnormalList.add("未查询到上报人员用户信息，上报人员米id：" + createdByMiId);
        }
        if (userGroupByMid.containsKey(salesmanMiId)) {
            IntlRmsUserNewDto salesyUserInfo = userGroupByMid.get(salesmanMiId);
            intlSoUserInfo.setSalesmanRmsAccount(ObjectUtils.defaultIfNull(salesyUserInfo.getDomainName(), ""));
            intlSoUserInfo.setSalesmanJobTitle(ObjectUtils.defaultIfNull(salesyUserInfo.getJobId(), 0));
        } else {
            abnormalList.add("未查询到销售人员用户信息，销售人员米id：" + salesmanMiId);
        }
        return intlSoUserInfo;
    }

    public IntlSoOrgInfo handleOrgInfo(Optional<RmsStoreInfoDto> storeInfoOpt,
                                       Optional<RmsPositionInfoRes> positionInfoOpt,
                                       Optional<IntlRetailerDTO> retailerOpt, RmsSyncQtyData data,
                                       List<String> abnormalList) {
        IntlSoOrgInfo intlSoOrgInfo = new IntlSoOrgInfo();
        // 使用ObjectUtils.defaultIfNull统一处理字符串字段
        intlSoOrgInfo.setStoreCode(ObjectUtils.defaultIfNull(data.getStoreCodeNew(), ""));
        intlSoOrgInfo.setStoreRmsCode(ObjectUtils.defaultIfNull(data.getStoreCodeRMS(), ""));
        intlSoOrgInfo.setPositionRmsCode(ObjectUtils.defaultIfNull(data.getPositionCodeRMS(), ""));
        intlSoOrgInfo.setPositionCode(ObjectUtils.defaultIfNull(data.getPositionCodeNew(), ""));
        intlSoOrgInfo.setRetailerCode(ObjectUtils.defaultIfNull(data.getRetailerCode(), ""));

        if (storeInfoOpt.isPresent()) {
            RmsStoreInfoDto rmsStoreInfoDto = storeInfoOpt.get();
            // 使用ObjectUtils.defaultIfNull统一处理数值字段
            intlSoOrgInfo.setStoreType(ObjectUtils.defaultIfNull(rmsStoreInfoDto.getType(), 0));
            intlSoOrgInfo.setStoreGrade(ObjectUtils.defaultIfNull(rmsStoreInfoDto.getGrade(), 0));
            intlSoOrgInfo.setStoreHasPC(ObjectUtils.defaultIfNull(rmsStoreInfoDto.getHasPc(), 0));
            intlSoOrgInfo.setStoreHasSR(ObjectUtils.defaultIfNull(rmsStoreInfoDto.getHasSR(), 0));
            intlSoOrgInfo.setStoreChannelType(ObjectUtils.defaultIfNull(rmsStoreInfoDto.getChannelType(), 0));
            intlSoOrgInfo.setCountryCode(ObjectUtils.defaultIfNull(rmsStoreInfoDto.getCountryShortcode(), ""));
            intlSoOrgInfo.setStoreId(ObjectUtils.defaultIfNull(rmsStoreInfoDto.getId(), 0));
        } else {
            abnormalList.add("未查询到门店信息，storeCodeRms：" + data.getStoreCodeRMS());
        }

        if (positionInfoOpt.isPresent()) {
            RmsPositionInfoRes rmsPositionInfoDto = positionInfoOpt.get();
            intlSoOrgInfo.setPositionType(ObjectUtils.defaultIfNull(rmsPositionInfoDto.getType(), 0));
            intlSoOrgInfo.setPositionId(ObjectUtils.defaultIfNull(rmsPositionInfoDto.getId(), 0));
        } else {
            abnormalList.add("未查询到阵地信息，positionCodeRms：" + data.getPositionCodeRMS());
        }

        if (retailerOpt.isPresent()) {
            IntlRetailerDTO intlRetailerDTO = retailerOpt.get();
            intlSoOrgInfo.setRetailerId(ObjectUtils.defaultIfNull(intlRetailerDTO.getId(), 0L));
        } else {
            abnormalList.add("未查询到零售商信息，retailerCode：" + data.getRetailerCode());
        }

        return intlSoOrgInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doQtyBatchSave(List<RmsSyncQtyData> qtyDataList) {
        log.info("----------doQtyBatchSave-----------, dataSize: {}", qtyDataList.size());
        if (CollectionUtils.isEmpty(qtyDataList)) {
            log.warn("doQtyBatchSave qtyDataList is empty");
            return;
        }

        // 1. 过滤掉已存在的数据
        List<RmsSyncQtyData> filteredDataList = filterExistingQtyData(qtyDataList);
        if (filteredDataList.isEmpty()) {
            log.info("All qty data already exists, no need to insert");
            return;
        }

        // 2. 批量获取外部依赖信息
        BatchQtyDependencyData batchDependencyData = fetchBatchQtyDependencyData(filteredDataList);

        // 3. 构建批量插入数据
        List<IntlSoQtyBatchSaveData> intlSoQtyBatchSaveData =
                buildQtyBatchInsertData(filteredDataList, batchDependencyData);

        // 4. 批量插入数据
        executeQtyBatchInsert(intlSoQtyBatchSaveData);

        log.info("Batch saved {} qty records", intlSoQtyBatchSaveData.size());
    }

    /**
     * 过滤掉已存在的数据
     */
    private List<RmsSyncQtyData> filterExistingQtyData(List<RmsSyncQtyData> qtyDataList) {
        List<String> rmsIds = qtyDataList.stream()
                .map(RmsSyncQtyData::getRmsId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (rmsIds.isEmpty()) {
            return qtyDataList;
        }

        List<IntlSoQty> existingQtys = this.lambdaQuery()
                .in(IntlSoQty::getRmsId, rmsIds)
                .list();

        Set<String> existingRmsIds = existingQtys.stream()
                .map(IntlSoQty::getRmsId)
                .collect(Collectors.toSet());

        return qtyDataList.stream()
                .filter(data -> !existingRmsIds.contains(data.getRmsId()))
                .collect(Collectors.toList());
    }

    /**
     * 批量获取依赖数据
     */
    private BatchQtyDependencyData fetchBatchQtyDependencyData(List<RmsSyncQtyData> qtyDataList) {
        BatchQtyDependencyData dependencyData = new BatchQtyDependencyData();

        // 获取用户信息
        dependencyData.userListOptional = fetchQtyUserList(qtyDataList);

        // 获取门店、阵地、零售商信息
        Map<String, Set<String>> codes = extractQtyCodes(qtyDataList);
        dependencyData.storeInfoMap = fetchQtyStoreInfo(codes.get("storeCodes"));
        dependencyData.positionInfoMap = fetchQtyPositionInfo(codes.get("positionCodes"));
        dependencyData.retailerInfoMap = fetchQtyRetailerInfo(codes.get("retailerCodes"));

        return dependencyData;
    }

    /**
     * 获取用户列表
     */
    private Optional<List<IntlRmsUserNewDto>> fetchQtyUserList(List<RmsSyncQtyData> qtyDataList) {
        Set<Long> userMiIds = new HashSet<>();
        for (RmsSyncQtyData data : qtyDataList) {
            Long createdByMiId = parseLong(data.getCreatedbyMiid());
            Long salesmanMiId = parseLong(data.getSalesmanMiid());
            if (!createdByMiId.equals(0L)) {
                userMiIds.add(createdByMiId);
            }
            if (!salesmanMiId.equals(0L)) {
                userMiIds.add(salesmanMiId);
            }
        }

        if (userMiIds.isEmpty()) {
            return Optional.empty();
        }

        return userApiService.getUserListByMiIds(new ArrayList<>(userMiIds));
    }

    /**
     * 提取各种代码
     */
    private Map<String, Set<String>> extractQtyCodes(List<RmsSyncQtyData> qtyDataList) {
        Set<String> storeCodes = qtyDataList.stream()
                .map(RmsSyncQtyData::getStoreCodeRMS)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toSet());

        Set<String> positionCodes = qtyDataList.stream()
                .map(RmsSyncQtyData::getPositionCodeRMS)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toSet());

        Set<String> retailerCodes = qtyDataList.stream()
                .map(RmsSyncQtyData::getRetailerCode)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toSet());

        Map<String, Set<String>> codes = new HashMap<>();
        codes.put("storeCodes", storeCodes);
        codes.put("positionCodes", positionCodes);
        codes.put("retailerCodes", retailerCodes);
        return codes;
    }

    /**
     * 获取门店信息
     */
    private Map<String, RmsStoreInfoDto> fetchQtyStoreInfo(Set<String> storeCodes) {
        Map<String, RmsStoreInfoDto> storeInfoMap = new HashMap<>();
        if (storeCodes.isEmpty()) {
            return storeInfoMap;
        }

        try {
            Map<String, RmsStoreInfoDto> batchStoreInfo =
                    rmsStoreService.batchGetStoreInfoByStoreCode(new ArrayList<>(storeCodes));
            storeInfoMap.putAll(batchStoreInfo);
        } catch (Exception e) {
            log.warn("Batch query store info failed, fallback to individual queries", e);
            // 降级到逐个查询
            for (String storeCode : storeCodes) {
                try {
                    Optional<RmsStoreInfoDto> storeInfo = rmsStoreService.getStoreInfoByStoreCode(storeCode);
                    storeInfo.ifPresent(info -> storeInfoMap.put(storeCode, info));
                } catch (Exception ex) {
                    log.warn("Query store info failed for storeCode: {}", storeCode, ex);
                }
            }
        }
        return storeInfoMap;
    }

    /**
     * 获取阵地信息
     */
    private Map<String, RmsPositionInfoRes> fetchQtyPositionInfo(Set<String> positionCodes) {
        Map<String, RmsPositionInfoRes> positionInfoMap = new HashMap<>();
        if (positionCodes.isEmpty()) {
            return positionInfoMap;
        }

        try {
            Map<String, RmsPositionInfoRes> batchPositionInfo =
                    positionApiService.getPositionInfoByPositionCodes(new ArrayList<>(positionCodes));
            positionInfoMap.putAll(batchPositionInfo);
        } catch (Exception e) {
            log.warn("Batch query position info failed, fallback to individual queries", e);
            // 降级到逐个查询
            for (String positionCode : positionCodes) {
                try {
                    Optional<RmsPositionInfoRes> positionInfo =
                            rmsStoreService.getPositionIfoByPositionCode(positionCode);
                    positionInfo.ifPresent(info -> positionInfoMap.put(positionCode, info));
                } catch (Exception ex) {
                    log.warn("Query position info failed for positionCode: {}", positionCode, ex);
                }
            }
        }
        return positionInfoMap;
    }

    /**
     * 获取零售商信息
     */
    private Map<String, IntlRetailerDTO> fetchQtyRetailerInfo(Set<String> retailerCodes) {
        Map<String, IntlRetailerDTO> retailerInfoMap = new HashMap<>();
        if (retailerCodes.isEmpty()) {
            return retailerInfoMap;
        }

        try {
            Map<String, IntlRetailerDTO> batchRetailerInfo =
                    retailerApiService.getRetailersByRetailerCodes(new ArrayList<>(retailerCodes));
            retailerInfoMap.putAll(batchRetailerInfo);
        } catch (Exception e) {
            log.warn("Batch query retailer info failed, fallback to individual queries", e);
            // 降级到逐个查询
            for (String retailerCode : retailerCodes) {
                try {
                    Optional<IntlRetailerDTO> retailerInfo = retailerApiService.getRetailerByRetailerCode(
                            new IntlPositionDTO().setRetailerCode(retailerCode));
                    retailerInfo.ifPresent(info -> retailerInfoMap.put(retailerCode, info));
                } catch (Exception ex) {
                    log.warn("Query retailer info failed for retailerCode: {}", retailerCode, ex);
                }
            }
        }
        return retailerInfoMap;
    }

    /**
     * 构建批量插入数据
     */
    private List<IntlSoQtyBatchSaveData> buildQtyBatchInsertData(List<RmsSyncQtyData> qtyDataList,
                                                                 BatchQtyDependencyData dependencyData) {
        List<IntlSoQtyBatchSaveData> batchInsertDataList = new ArrayList<>();

        for (RmsSyncQtyData data : qtyDataList) {

            // 构建用户信息
            Long createdByMiId = parseLong(data.getCreatedbyMiid());
            Long salesmanMiId = parseLong(data.getSalesmanMiid());
            List<String> abnormalList = new ArrayList<>();
            IntlSoUserInfo userInfo =
                    handleUserInfo(dependencyData.userListOptional, createdByMiId, salesmanMiId, abnormalList);
            intlSoUserInfoMapper.insert(userInfo);

            // 构建组织信息
            IntlSoOrgInfo orgInfo = handleOrgInfo(
                    Optional.ofNullable(dependencyData.storeInfoMap.get(data.getStoreCodeRMS())),
                    Optional.ofNullable(dependencyData.positionInfoMap.get(data.getPositionCodeRMS())),
                    Optional.ofNullable(dependencyData.retailerInfoMap.get(data.getRetailerCode())),
                    data,
                    abnormalList
            );
            intlSoOrgInfoMapper.insert(orgInfo);

            // 构建Qty实体
            IntlSoQtyBatchSaveData qtyEntity = new IntlSoQtyBatchSaveData();
            ComponentLocator.getConverter().convert(data, qtyEntity);

            qtyEntity.setUserInfoId(userInfo.getId());
            qtyEntity.setOrgInfoId(orgInfo.getId());
            qtyEntity.setReportingType(data.getReportType());
            qtyEntity.setStoreRmsCode(data.getStoreCodeRMS());
            qtyEntity.setPositionRmsCode(data.getPositionCodeRMS());
            qtyEntity.setRetailerCode(data.getRetailerCode());
            qtyEntity.setSalesmanMid(salesmanMiId);
            qtyEntity.setCreatedby(createdByMiId);
            qtyEntity.setModifiedby(parseLong(data.getModifiedbyMiId()));
            qtyEntity.setRrpCode(data.getRrpRMSCode());
            qtyEntity.setDataFrom(DataFromEnum.RMS.getCode());
            qtyEntity.setAbnormalList(abnormalList);
            qtyEntity.setStockId(data.getIdNew());
            batchInsertDataList.add(qtyEntity);
        }

        return batchInsertDataList;
    }

    /**
     * 执行批量插入
     */
    private void executeQtyBatchInsert(List<IntlSoQtyBatchSaveData> qtyEntities) {
        if (CollectionUtils.isEmpty(qtyEntities)) {
            return;
        }

        List<IntlDatasyncLog> syncLogs = new ArrayList<>();
        List<Long> stockIdList = new ArrayList<>();

        // 3. 批量插入Qty数据
        if (!qtyEntities.isEmpty()) {
            baseMapper.batchInsert(qtyEntities);
        }

        // 4. 构建同步日志
        for (IntlSoQtyBatchSaveData qtyData : qtyEntities) {
            IntlDatasyncLog syncLog = new IntlDatasyncLog()
                    .setType(DataSyncDataTypeEnum.QTY.getCode())
                    .setRmsId(qtyData.getRmsId())
                    .setRetailId(qtyData.getId())
                    .setMessage(JSON.toJSONString(qtyData))
                    .setOperateType(DataSyncOperateTypeTypeEnum.CREATE.getCode());

            if (CollectionUtils.isNotEmpty(qtyData.getAbnormalList())) {
                syncLog.setIsDataAbnormal(DataAbnormalEnum.DATA_ABNORMAL.getCode());
                syncLog.setAbnormalMessage(String.join(";", qtyData.getAbnormalList()));
            }
            syncLogs.add(syncLog);
            if (ObjectUtil.isNotNull(qtyData.getIsStockData()) &&
                    qtyData.getIsStockData() == 1) {
                stockIdList.add(qtyData.getStockId());
            }
        }

        // 5. 批量插入同步日志
        if (!syncLogs.isEmpty()) {
            intlDatasyncLogMapper.batchInsert(syncLogs);
        }

        // 6. 批量更新存量数据同步状态
        if (CollectionUtils.isNotEmpty(stockIdList)) {
            StockDataSyncReqDto stockDataSyncReqDto =
                    new StockDataSyncReqDto().setSyncStatus(SyncStatusEnum.SUCCESS.getCode())
                            .setSyncEndTime(LocalDateTime.now())
                            .setIdList(stockIdList);
            rmsStockDataSyncMapper.updateQtySyncStatus(stockDataSyncReqDto);
        }
    }

    /**
     * 批量依赖数据类
     */
    private static class BatchQtyDependencyData {
        Optional<List<IntlRmsUserNewDto>> userListOptional = Optional.empty();
        Map<String, RmsStoreInfoDto> storeInfoMap = new HashMap<>();
        Map<String, RmsPositionInfoRes> positionInfoMap = new HashMap<>();
        Map<String, IntlRetailerDTO> retailerInfoMap = new HashMap<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doQtyBatchUpdate(List<RmsSyncQtyData> qtyDataList) {
        log.info("----------doQtyBatchUpdate-----------, dataSize: {}", qtyDataList.size());
        if (CollectionUtils.isEmpty(qtyDataList)) {
            log.warn("doQtyBatchUpdate qtyDataList is empty");
            return;
        }

        // 1. 批量查询现有数据
        List<String> rmsIds = qtyDataList.stream()
                .map(RmsSyncQtyData::getRmsId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (rmsIds.isEmpty()) {
            log.warn("No valid rmsIds found for batch update");
            return;
        }

        List<IntlSoQty> existingQtys = this.lambdaQuery()
                .in(IntlSoQty::getRmsId, rmsIds)
                .list();

        if (CollectionUtils.isEmpty(existingQtys)) {
            log.warn("No existing qty data found for batch update");
            return;
        }

        // 构建rmsId到实体的映射
        Map<String, IntlSoQty> existingQtyMap = existingQtys.stream()
                .collect(Collectors.toMap(IntlSoQty::getRmsId, qty -> qty));

        // 2. 构建批量更新数据和日志
        List<IntlSoQty> updateQtys = new ArrayList<>();
        List<IntlDatasyncLog> syncLogs = new ArrayList<>();

        for (RmsSyncQtyData data : qtyDataList) {
            IntlSoQty existingQty = existingQtyMap.get(data.getRmsId());
            if (existingQty == null) {
                log.warn("Qty data not found for rmsId: {}", data.getRmsId());
                continue;
            }

            // 更新字段
            existingQty.setStatus(data.getStatus());
            existingQty.setModifiedon(data.getModifiedon());
            existingQty.setModifiedby(parseLong(data.getModifiedbyMiId()));

            updateQtys.add(existingQty);
            data.setId(existingQty.getId());

            // 构建同步日志
            IntlDatasyncLog syncLog = new IntlDatasyncLog()
                    .setType(DataSyncDataTypeEnum.QTY.getCode())
                    .setRmsId(data.getRmsId())
                    .setRetailId(existingQty.getId())
                    .setMessage(JSON.toJSONString(data))
                    .setOperateType(DataSyncOperateTypeTypeEnum.UPDATE.getCode());

            syncLogs.add(syncLog);
        }

        // 3. 批量更新
        if (!updateQtys.isEmpty()) {
            this.updateBatchById(updateQtys);
        }

        // 4. 批量插入同步日志
        if (!syncLogs.isEmpty()) {
            intlDatasyncLogMapper.batchInsert(syncLogs);
        }

        List<Long> stockIdList =
                qtyDataList.stream()
                        .filter(imeiEntity -> ObjectUtil.isNotNull(imeiEntity.getIsStockData()) &&
                                imeiEntity.getIsStockData() == 1).map(RmsSyncQtyData::getIdNew)
                        .collect(Collectors.toList());

        // 5. 批量更新存量数据同步状态
        if (CollectionUtils.isNotEmpty(stockIdList)) {
            StockDataSyncReqDto stockDataSyncReqDto =
                    new StockDataSyncReqDto().setSyncStatus(SyncStatusEnum.SUCCESS.getCode())
                            .setSyncEndTime(LocalDateTime.now())
                            .setIdList(stockIdList);
            rmsStockDataSyncMapper.updateQtySyncStatus(stockDataSyncReqDto);
        }
        log.info("Batch updated {} qty records", updateQtys.size());
    }

    @Override
    public List<IntlSoQty> batchGetQtyByRmsId(List<String> rmsIds) {
        if (rmsIds == null || rmsIds.isEmpty()) {
            return java.util.Collections.emptyList();
        }

        return this.lambdaQuery()
                .in(IntlSoQty::getRmsId, rmsIds)
                .select(IntlSoQty::getId, IntlSoQty::getRmsId)
                .list();
    }

    @Override
    public int updateIsPhotoExistByDetailIds(List<String> detailIds) {
        if (CollectionUtils.isEmpty(detailIds)) {
            log.warn("updateIsPhotoExistByDetailIds: detailIds is empty");
            return 0;
        }

        log.info("updateIsPhotoExistByDetailIds: updating {} records", detailIds.size());

        LambdaUpdateWrapper<IntlSoQty> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(IntlSoQty::getIsPhotoExist, 1)
                    .in(IntlSoQty::getDetailId, detailIds);

        int updateCount = baseMapper.update(null, updateWrapper);
        log.info("updateIsPhotoExistByDetailIds: updated {} records", updateCount);

        return updateCount;
    }

}
