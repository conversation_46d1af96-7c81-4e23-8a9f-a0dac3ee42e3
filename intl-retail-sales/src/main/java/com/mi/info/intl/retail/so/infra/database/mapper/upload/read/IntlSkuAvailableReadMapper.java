package com.mi.info.intl.retail.so.infra.database.mapper.upload.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.info.intl.retail.intlretail.service.api.so.sys.dto.SkuAvailableSearch;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSkuAvailable;
import com.mi.info.intl.retail.so.infra.dto.IntlSkuAvailableDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【intl_sku_available(SKU 可用性信息表)】的数据库操作Mapper
 * @createDate 2025-07-24 20:05:34
 * @Entity com.mi.info.intl.retail.so.infra.entity.IntlSkuAvailable
 */
public interface IntlSkuAvailableReadMapper extends BaseMapper<IntlSkuAvailable> {

    List<IntlSkuAvailableDetail> getDataBySearch(@Param("startNum") int startNum, @Param("limit") int limit,
                                                 @Param("searchBody") SkuAvailableSearch searchBody);

    int getCountBySearch(@Param("searchBody") SkuAvailableSearch searchBody);
}




