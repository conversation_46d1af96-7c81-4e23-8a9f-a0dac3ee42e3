package com.mi.info.intl.retail.so.util;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.mi.info.intl.retail.so.util.dto.ExcelFileResource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.List;

/**
 * 文件处理工具类
 *
 * <AUTHOR>
 * @date 2025/8/14
 */
@Slf4j
public class ExcelFileUtil {

    private static final String FILE_SUFFIX = ".xlsx";
    // 添加私有构造函数，防止实例化
    private ExcelFileUtil() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 生成Excel文件（使用try-with-resources自动管理）
     *
     * @param dataList  数据列表
     * @param clazz     数据类类型
     * @param sheetName 工作表名称
     * @return 可自动关闭的Excel文件资源
     */
    public static <T> ExcelFileResource generateExcelFile(
            List<T> dataList,
            Class<T> clazz,
            String sheetName) throws IOException {

        // 参数校验
        if (dataList == null || clazz == null) {
            throw new IllegalArgumentException("Data list and class type cannot be null");
        }

        // 创建临时文件
        File tempFile = Files.createTempFile("excel_", FILE_SUFFIX).toFile();

        try {
            // 设置默认工作表名称
            String finalSheetName = StringUtils.isBlank(sheetName) ? "Sheet1" : sheetName;

            // 写入Excel数据
            try (ExcelWriter excelWriter = EasyExcelFactory.write(tempFile, clazz)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .build()) {

                WriteSheet writeSheet = EasyExcelFactory.writerSheet(finalSheetName).build();
                excelWriter.write(dataList, writeSheet);
            }

            log.debug("Excel文件生成成功: {}", tempFile.getAbsolutePath());
            return new ExcelFileResource(tempFile, true);

        } catch (Exception e) {
            // 如果出现异常，确保删除临时文件
            if (tempFile.exists()) {
                Files.delete(tempFile.toPath());
            }
            throw new IOException("生成Excel文件失败", e);
        }
    }

    /**
     * 从URL下载文件
     */
    public static ExcelFileResource downloadFileFromUrl(String fileUrl) throws IOException {
        OkHttpClient client = new OkHttpClient();
        Request request = new Request.Builder().url(fileUrl).build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("文件下载失败，HTTP状态码: " + response.code());
            }

            // 创建临时文件
            File tempFile = Files.createTempFile("excel_", FILE_SUFFIX).toFile();
            try (InputStream inputStream = response.body().byteStream();
                 FileOutputStream outputStream = new FileOutputStream(tempFile)) {

                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            }

            return new ExcelFileResource(tempFile, true);
        }
    }

    /**
     * 生成标准化的文件名
     *
     * @param prefix 文件前缀（可为空，默认"export_"）
     * @param suffix 文件后缀（可为空，默认".xlsx"）
     * @return 生成的文件名（格式：prefix + 时间戳 + suffix）
     */
    public static String generateExcelFileName(String prefix, String suffix) {
        // 设置默认值
        String finalPrefix = StringUtils.isBlank(prefix) ? "export_" : prefix;
        String finalSuffix = StringUtils.isBlank(suffix) ? FILE_SUFFIX : suffix;

        // 生成时间戳
        String timestamp = String.valueOf(System.currentTimeMillis() / 1000L);

        return finalPrefix + timestamp + finalSuffix;
    }

    /**
     * 校验Excel文件表头是否与模板匹配
     * @param inputFile      已下载的文件
     * @param expectedHeaders 期望的表头数组
     */
    public static boolean validateExcelHeaders(File inputFile, String[] expectedHeaders) {
        log.info("开始校验Excel表头");

        try (InputStream inputStream = Files.newInputStream(inputFile.toPath());
             Workbook workbook = new XSSFWorkbook(inputStream)) {

            Sheet sheet = workbook.getSheetAt(0);
            Row headerRow = sheet.getRow(0);

            if (headerRow == null) {
                return false;
            }

            // 检查表头数量是否匹配
            int actualHeaderCount = headerRow.getLastCellNum();
            if (actualHeaderCount < expectedHeaders.length) {
                log.warn("表头数量不匹配，期望: {}, 实际: {}", expectedHeaders.length, actualHeaderCount);
                return false;
            }

            // 逐个检查表头内容
            for (int i = 0; i < expectedHeaders.length; i++) {
                Cell cell = headerRow.getCell(i);
                String actualHeader = getCellValueAsString(cell);
                String expectedHeader = expectedHeaders[i];

                if (!expectedHeader.equals(actualHeader)) {
                    log.warn("表头不匹配，位置: {}, 期望: '{}', 实际: '{}'", i, expectedHeader, actualHeader);
                    return false;
                }
            }

            log.info("Excel表头校验通过");
            return true;

        } catch (Exception e) {
            log.error("校验Excel表头时发生异常", e);
            return false;
        }
    }
    /**
     * 获取单元格值作为字符串
     */
    private static String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                return String.valueOf((long) cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

}
