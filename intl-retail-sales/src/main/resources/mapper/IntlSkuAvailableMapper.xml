<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSkuAvailableMapper">

    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.so.domain.upload.entity.IntlSkuAvailable">
            <id property="id" column="id" />
            <result property="countryCode" column="country_code" />
            <result property="productCode" column="product_code" />
            <result property="deliverytime" column="deliverytime" />
            <result property="salestime" column="salestime" />
            <result property="status" column="status" />
            <result property="createdBy" column="createdby" />
            <result property="createdOn" column="createdon" />
            <result property="modifiedBy" column="modifiedby" />
            <result property="modifiedOn" column="modifiedon" />
    </resultMap>

    <sql id="Base_Column_List">
        id,country_code,product_code,deliverytime,salestime,status,
        createdby,createdon,modifiedby,modifiedon
    </sql>
</mapper>
