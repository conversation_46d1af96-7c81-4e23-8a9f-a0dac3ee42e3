<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoImeiMapper">

    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.so.domain.upload.entity.IntlSoImei">
        <id property="id" column="id"/>
        <result property="sn" column="sn"/>
        <result property="imei1" column="imei1"/>
        <result property="imei2" column="imei2"/>
        <result property="snMask" column="sn_mask"/>
        <result property="imei1Mask" column="imei1_mask"/>
        <result property="imei2Mask" column="imei2_mask"/>
        <result property="snHash" column="sn_hash"/>
        <result property="imeiFromHub" column="imei_from_hub"/>
        <result property="productCode" column="product_code"/>
        <result property="orgInfoId" column="org_info_id"/>
        <result property="userInfoId" column="user_info_id"/>
        <result property="focusModel" column="focus_model"/>
        <result property="imeiRuleIsActivingCheck" column="imei_rule_is_activing_check"/>
        <result property="imeiRuleId" column="imei_rule_id"/>
        <result property="imeiRuleBefore" column="imei_rule_before"/>
        <result property="imeiRuleAfter" column="imei_rule_after"/>
        <result property="activationVerificationTime" column="activation_verification_time"/>
        <result property="activationTime" column="activation_time"/>
        <result property="activationFrequency" column="activation_frequency"/>
        <result property="activationSite" column="activation_site"/>
        <result property="currency" column="currency"/>
        <result property="verifyingState" column="verifying_state"/>
        <result property="verificationResult" column="verification_result"/>
        <result property="verifyResultDetail" column="verify_result_detail"/>
        <result property="repeatUser" column="repeat_user"/>
        <result property="failedReason" column="failed_reason"/>
        <result property="failedReasonDetail" column="failed_reason_detail"/>
        <result property="siVerifyResult" column="si_verify_result"/>
        <result property="reportingType" column="reporting_type"/>
        <result property="lastMd" column="last_md"/>
        <result property="firstLevelAccountCode" column="first_level_account_code"/>
        <result property="finalSalesCountry" column="final_sales_country"/>
        <result property="allowSalesCountry" column="allow_sales_country"/>
        <result property="note" column="note"/>
        <result property="rrpCode" column="rrp_code"/>
        <result property="rrp" column="rrp"/>
        <result property="batchId" column="batch_id"/>
        <result property="salesTime" column="sales_time"/>
        <result property="createdBy" column="created_by"/>
        <result property="createdOn" column="created_on"/>
        <result property="modifiedBy" column="modified_by"/>
        <result property="modifiedOn" column="modified_on"/>
        <result property="status" column="status"/>
        <result property="storeRmsCode" column="store_rms_code"/>
        <result property="positionRmsCode" column="position_rms_code"/>
        <result property="rmsId" column="rms_id"/>
        <result property="isPhotoExist" column="is_photo_exist"/>
        <result property="dataFrom" column="data_from"/>
        <result property="detailId" column="detail_id"/>
        <result property="salesChannel" column="sales_channel"/>
        <result property="batchIdStr" column="batch_id_str"/>
        <result property="createdOnNew" column="created_on_new"/>
        <result property="supplierCode" column="supplier_code"/>
        <result property="updateOnNew" column="update_on_new"/>
        <result property="stockId" column="stock_id"/>
        <result property="productLineCode" column="product_line_code"/>
        <result property="productName" column="product_name"/>
        <result property="productShortName" column="product_short_name"/>
    </resultMap>

    <sql id="Base_Column_List">
         id, sn, imei1, imei2, sn_mask, imei1_mask, imei2_mask, sn_hash, imei_from_hub,
         product_code, org_info_id, user_info_id, focus_model, imei_rule_id, imei_rule_before,
         imei_rule_after, activation_verification_time, activation_time, activation_frequency,
         activation_site, currency, verifying_state, verification_result, verify_result_detail,
         repeat_user, failed_reason, failed_reason_detail, si_verify_result, reporting_type,
         first_level_account_code, final_sales_country, allow_sales_country, note, rrp_code,
         rrp, batch_id, sales_time, created_by, created_on, modified_by, modified_on, status,
         store_rms_code, position_rms_code, rms_id, is_photo_exist, data_from, detail_id,
         sales_channel, batch_id_str, created_on_new, supplier_code, update_on_new, stock_id,
         product_line_code, product_name, product_short_name
    </sql>

    <!-- 批量插入IMEI数据 -->
    <insert id="batchInsert" parameterType="com.mi.info.intl.retail.so.domain.datasync.dto.IntlSoImeiBatchSaveData">
        INSERT INTO intl_so_imei (
        sn,imei1,imei2,sn_mask,imei1_mask,imei2_mask,sn_hash,imei_from_hub,
        product_code,org_info_id,user_info_id,
        imei_rule_before,imei_rule_after,activation_verification_time,activation_time,
        activation_frequency,activation_site,currency,verifying_state,verification_result,
        verify_result_detail,failed_reason,failed_reason_detail,si_verify_result,
        reporting_type,first_level_account_code,final_sales_country,allow_sales_country,
        note,rrp,sales_time,created_by,created_on,rrp_code,store_rms_code,position_rms_code,supplier_code,
        modified_by,modified_on,status,rms_id,data_from,stock_id
        ) VALUES
        <foreach collection="imeiList" item="item" separator=",">
            (
            #{item.sn},#{item.imei1},#{item.imei2},#{item.snMask},#{item.imei1Mask},
            #{item.imei2Mask},#{item.snHash},#{item.imeiFromHub},#{item.productCode},
            #{item.orgInfoId},#{item.userInfoId},
            #{item.imeiRuleBefore},#{item.imeiRuleAfter},#{item.activationVerificationTime},
            #{item.activationTime},#{item.activationFrequency},#{item.activationSite},
            #{item.currency},#{item.verifyingState},#{item.verificationResult},
            #{item.verifyResultDetail},#{item.failedReason},#{item.failedReasonDetail},
            #{item.siVerifyResult},#{item.reportingType},#{item.firstLevelAccountCode},
            #{item.finalSalesCountry},#{item.allowSalesCountry},#{item.note},
            #{item.rrp},#{item.salesTime},#{item.createdBy},#{item.createdOn},#{item.rrpCode},
            #{item.storeRmsCode},#{item.positionRmsCode},#{item.supplierCode},
            #{item.modifiedBy},#{item.modifiedOn},#{item.status},
            #{item.rmsId},#{item.dataFrom},#{item.stockId}
            )
        </foreach>
    </insert>

    <resultMap id="DuplicateResultMap" type="com.mi.info.intl.retail.so.domain.upload.entity.IntlSoImei">
        <id property="id" column="id"/>
        <result property="snHash" column="sn_hash"/>
        <result property="sn" column="sn"/>
    </resultMap>

    <!-- 查询重复的IMEI记录 -->
    <select id="queryDuplicateRecords" resultMap="DuplicateResultMap">
        SELECT s.id, s.sn_hash, s.sn
        FROM intl_so_imei s
        INNER JOIN intl_so_user_info u ON u.id = s.user_info_id
        INNER JOIN intl_so_org_info o ON o.id = s.org_info_id
        WHERE s.status = 0
        AND s.sn_hash IN
        <foreach collection="snHashes" item="hash" open="(" separator="," close=")">
            #{hash}
        </foreach>
        AND s.verification_result IN (100000000, 100000002)
        AND u.salesman_jobtitle IN
        <foreach collection="jobTitleCodes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
        AND o.country_code = #{countryCode}
        AND s.created_on >= #{threeMonthsAgo}
    </select>

    <!-- 分页查询IMEI明细列表 -->
    <select id="queryImeiDetailList" resultType="java.util.Map">
        SELECT
        imei.id,
        imei.product_name as productName,
        imei.imei1_mask as imei1Mask,
        imei.imei2_mask as imei2Mask,
        imei.sn_mask as snMask,
        imei.note,
        s.name as storeName,
        imei.verification_result as verifyResult,
        imei.verify_result_detail as verifyResultDetail,
        imei.sales_time,
        org.country_code
        FROM intl_so_imei imei
        JOIN intl_so_org_info org ON imei.org_info_id = org.id
        JOIN intl_rms_store s ON org.store_id = s.id
        WHERE 1=1
        AND org.country_code = #{countryCode}
        AND imei.status = 0
        <choose>
            <when test="userStoreIds != null and userStoreIds.size() > 0">
                AND org.store_id IN
                <foreach collection="userStoreIds" item="storeId" open="(" separator="," close=")">
                    #{storeId}
                </foreach>
            </when>
            <otherwise>
                AND imei.salesman_mid = #{miId}
            </otherwise>
        </choose>
        <if test="search != null and search != ''">
            AND (
            imei.imei1_mask LIKE CONCAT('%', #{search}, '%')
            OR imei.imei2_mask LIKE CONCAT('%', #{search}, '%')
            OR imei.sn_mask LIKE CONCAT('%', #{search}, '%')
            OR imei.product_name LIKE CONCAT('%', #{search}, '%')
            )
        </if>
        <if test="reportingType != null">
            AND imei.reporting_type = #{reportingType}
        </if>
        <if test="storeCode != null and storeCode.size() > 0">
            AND imei.store_rms_code IN
            <foreach collection="storeCode" item="scode" open="(" separator="," close=")">
                #{scode}
            </foreach>
        </if>
        <if test="productLine != null and productLine.size() > 0">
            AND imei.product_line_code IN
            <foreach collection="productLine" item="line" open="(" separator="," close=")">
                #{line}
            </foreach>
        </if>
        <if test="storeType != null and storeType.size() > 0">
            AND org.store_type IN
            <foreach collection="storeType" item="stype" open="(" separator="," close=")">
                #{stype}
            </foreach>
        </if>
        <if test="channelType != null and channelType.size() > 0">
            AND org.store_channel_type IN
            <foreach collection="channelType" item="ctype" open="(" separator="," close=")">
                #{ctype}
            </foreach>
        </if>
        <if test="verifyResult != null">
            AND imei.verification_result = #{verifyResult}
        </if>
        <if test="startTime != null and endTime != null">
            AND imei.sales_time >= #{startTime}
            AND imei.sales_time &lt; #{endTime}
        </if>
        ORDER BY imei.sales_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 查询IMEI明细总数 -->
    <select id="countImeiDetailList" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM intl_so_imei imei
        JOIN intl_so_org_info org ON imei.org_info_id = org.id
        WHERE 1=1
        AND org.country_code = #{countryCode}
        AND imei.status = 0
        <choose>
            <when test="userStoreIds != null and userStoreIds.size() > 0">
                AND org.store_id IN
                <foreach collection="userStoreIds" item="storeId" open="(" separator="," close=")">
                    #{storeId}
                </foreach>
            </when>
            <otherwise>
                AND imei.salesman_mid = #{miId}
            </otherwise>
        </choose>
        <if test="search != null and search != ''">
            AND (
            imei.imei1_mask LIKE CONCAT('%', #{search}, '%')
            OR imei.imei2_mask LIKE CONCAT('%', #{search}, '%')
            OR imei.sn_mask LIKE CONCAT('%', #{search}, '%')
            OR imei.product_name LIKE CONCAT('%', #{search}, '%')
            )
        </if>
        <if test="reportingType != null">
            AND imei.reporting_type = #{reportingType}
        </if>
        <if test="storeCode != null and storeCode.size() > 0">
            AND imei.store_rms_code IN
            <foreach collection="storeCode" item="scode" open="(" separator="," close=")">
                #{scode}
            </foreach>
        </if>
        <if test="productLine != null and productLine.size() > 0">
            AND imei.product_line_code IN
            <foreach collection="productLine" item="line" open="(" separator="," close=")">
                #{line}
            </foreach>
        </if>
        <if test="storeType != null and storeType.size() > 0">
            AND org.store_type IN
            <foreach collection="storeType" item="stype" open="(" separator="," close=")">
                #{stype}
            </foreach>
        </if>
        <if test="channelType != null and channelType.size() > 0">
            AND org.store_channel_type IN
            <foreach collection="channelType" item="ctype" open="(" separator="," close=")">
                #{ctype}
            </foreach>
        </if>
        <if test="verifyResult != null">
            AND imei.verification_result = #{verifyResult}
        </if>
        <if test="startTime != null and endTime != null">
            AND imei.sales_time >= #{startTime}
            AND imei.sales_time &lt; #{endTime}
        </if>
    </select>

    <!-- 根据ID查询IMEI明细 -->
    <select id="queryImeiDetailById" resultType="java.util.Map">
        SELECT imei.id,
               imei.product_name            as productName,
               imei.imei1_mask           as imei1Mask,
               imei.imei2_mask           as imei2Mask,
               imei.sn_mask              as snMask,
               imei.note,
               s.name                    as storeName,
               imei.verification_result  as verifyResult,
               imei.verify_result_detail as verifyResultDetail,
               u.english_name            as createdBy,
               imei.reporting_type       as reportingType,
               imei.created_on           as createdOn,
               org.country_code          as countryCode,
               f.fds_url                 as url
        FROM intl_so_imei imei
        JOIN intl_so_org_info org ON imei.org_info_id = org.id
        JOIN intl_rms_store s ON org.store_id = s.id
        JOIN intl_so_user_info user ON imei.user_info_id = user.id
        JOIN intl_rms_user u ON user.createdby_mid = u.mi_id
        LEFT JOIN intl_file_upload f ON imei.detail_id = f.guid
        WHERE imei.id = #{imeiId}
    </select>

    <!-- 查询IMEI汇总数据 -->
    <select id="queryImeiSummary" resultType="java.util.Map">
        SELECT
        imei.verification_result,
        COUNT(1) as count
        FROM intl_so_imei imei
        JOIN intl_so_org_info org ON imei.org_info_id = org.id
        WHERE 1=1
        AND org.country_code = #{countryCode}
        AND imei.status = 0
        <choose>
            <when test="userStoreIds != null and userStoreIds.size() > 0">
                AND org.store_id IN
                <foreach collection="userStoreIds" item="storeId" open="(" separator="," close=")">
                    #{storeId}
                </foreach>
            </when>
            <otherwise>
                AND imei.salesman_mid = #{miId}
            </otherwise>
        </choose>
        <if test="search != null and search != ''">
            AND (
            imei.imei1_mask LIKE CONCAT('%', #{search}, '%')
            OR imei.imei2_mask LIKE CONCAT('%', #{search}, '%')
            OR imei.sn_mask LIKE CONCAT('%', #{search}, '%')
            OR imei.product_name LIKE CONCAT('%', #{search}, '%')
            )
        </if>
        <if test="storeCode != null and storeCode.size() > 0">
            AND imei.store_rms_code IN
            <foreach collection="storeCode" item="scode" open="(" separator="," close=")">
                #{scode}
            </foreach>
        </if>
        <if test="productLine != null and productLine.size() > 0">
            AND imei.product_line_code IN
            <foreach collection="productLine" item="line" open="(" separator="," close=")">
                #{line}
            </foreach>
        </if>
        <if test="storeType != null and storeType.size() > 0">
            AND org.store_type IN
            <foreach collection="storeType" item="stype" open="(" separator="," close=")">
                #{stype}
            </foreach>
        </if>
        <if test="channelType != null and channelType.size() > 0">
            AND org.store_channel_type IN
            <foreach collection="channelType" item="ctype" open="(" separator="," close=")">
                #{ctype}
            </foreach>
        </if>
        <if test="verifyResult != null">
            AND imei.verification_result = #{verifyResult}
        </if>
        <if test="startTime != null and endTime != null">
            AND imei.sales_time >= #{startTime}
            AND imei.sales_time &lt; #{endTime}
        </if>
        GROUP BY imei.verification_result
    </select>

    <!-- 根据ID批量更新指定字段 -->
    <update id="batchUpdateById" parameterType="java.util.List">
        <foreach collection="imeiList" item="item" separator=";">
            UPDATE intl_so_imei
            SET
            activation_frequency = #{item.activationFrequency},
            activation_verification_time = #{item.activationVerificationTime},
            verification_result = #{item.verificationResult},
            verify_result_detail = #{item.verifyResultDetail},
            failed_reason = #{item.failedReason},
            activation_time = #{item.activationTime},
            activation_site = #{item.activationSite}
            WHERE id = #{item.id}
        </foreach>
    </update>

    <select id="selectByRetailerIds" resultType="com.mi.info.intl.retail.so.domain.upload.entity.IntlSoImei">
        SELECT id, rms_id FROM intl_so_imei
        where
        id IN
        <foreach collection="retailIds" item="retailId" open="(" separator="," close=")">
            #{retailId}
        </foreach>
    </select>

    <select id="selectByRmsIds" resultType="com.mi.info.intl.retail.so.domain.upload.entity.IntlSoImei">
        SELECT id, rms_id FROM intl_so_imei
        where
        rms_id IN
        <foreach collection="rmsIds" item="rmsId" open="(" separator="," close=")">
            #{rmsId}
        </foreach>
    </select>

    <update id="batchUpdate">
        <foreach collection="list" item="item" separator=";">
            UPDATE intl_so_imei
            SET
            imei_rule_is_activing_check = #{item.imeiRuleIsActivingCheck},
            imei_rule_before = #{item.imeiRuleBefore},
            imei_rule_after = #{item.imeiRuleAfter},
            activation_verification_time = #{item.activationVerificationTime},
            verifying_state = #{item.verifyingState},
            activation_time = #{item.activationTime},
            activation_frequency = #{item.activationFrequency},
            activation_site = #{item.activationSite},
            si_verify_result = #{item.siVerifyResult},
            verify_result_detail = #{item.verifyResultDetail},
            verification_result = #{item.verificationResult},
            repeat_user = #{item.repeatUser},
            final_sales_country = #{item.finalSalesCountry},
            failed_reason = #{item.failedReason},
            failed_reason_detail = #{item.failedReasonDetail},
            first_level_account_code = #{item.firstLevelAccountCode},
            modified_on = #{item.modifiedOn},
            modified_by = #{item.modifiedBy}
            WHERE id = #{item.id} AND modified_on &lt; #{item.modifiedOn}
        </foreach>
    </update>

</mapper>
