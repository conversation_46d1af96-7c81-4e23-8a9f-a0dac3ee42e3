package com.mi.info.intl.retail.so.app;

import com.alibaba.fastjson.JSONObject;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsSyncDataRequest;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import com.mi.info.intl.retail.so.app.mq.dto.RmsSyncSoDataReqDto;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.messaging.Message;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RmsSyncSoDataServiceImplTest {

    @Mock
    private RocketMQTemplate rocketMQTemplate;

    @InjectMocks
    private RmsSyncSoDataServiceImpl service;

    @BeforeEach
    void init() throws Exception {
        // 通过反射设置私有配置字段
        setField(service, "topic", "test-topic");
        setField(service, "batchSendMqSize", 500);
        setField(service, "acceptRmsMaxSize", 1000);
        setField(service, "batchSaveSize", 2);
    }

    @Test
    @DisplayName("syncRmsSoData 成功发送，按条转消息")
    void syncRmsSoData_success() {
        RmsSyncDataRequest req = buildRequest("IMEI", "create", Arrays.asList(mockJson(), mockJson()));
        when(rocketMQTemplate.syncSend(anyString(), anyList())).thenReturn(mock(SendResult.class));

        CommonResponse<String> resp = service.syncRmsSoData(req);

        assertNotNull(resp);
        assertEquals("success", resp.getData());
        // 捕获消息入参，校验按条转换
        @SuppressWarnings("unchecked")
        ArgumentCaptor<List<Message<List<RmsSyncSoDataReqDto>>>> captor = ArgumentCaptor.forClass(List.class);
        verify(rocketMQTemplate, times(1)).syncSend(eq("test-topic"), captor.capture());
        List<Message<List<RmsSyncSoDataReqDto>>> messages = captor.getValue();
        assertEquals(2, messages.size());
    }

    @Test
    @DisplayName("syncRmsSoData dataList 为空抛出 BizException")
    void syncRmsSoData_empty_throw() {
        RmsSyncDataRequest req = buildRequest("IMEI", "create", new ArrayList<>());
        BizException ex = assertThrows(BizException.class, () -> service.syncRmsSoData(req));
        assertTrue(ex.getMessage().contains("dataList error"));
    }

    @Test
    @DisplayName("syncRmsSoData 发送异常向外抛出")
    void syncRmsSoData_send_throw() {
        RmsSyncDataRequest req = buildRequest("IMEI", "create", Arrays.asList(mockJson()));
        when(rocketMQTemplate.syncSend(anyString(), anyList())).thenThrow(new RuntimeException("mq error"));
        assertThrows(RuntimeException.class, () -> service.syncRmsSoData(req));
    }

    @Test
    @DisplayName("bathSyncRmsSoData 成功发送，按批分组")
    void bathSyncRmsSoData_success_batching() throws Exception {
        // batchSaveSize=2，准备3条数据，应发送2条消息（2条+1条）
        setField(service, "batchSendMqSize", 100); // 不触发外层分批
        setField(service, "batchSaveSize", 2);
        List<JSONObject> list = Arrays.asList(mockJson(), mockJson(), mockJson());
        RmsSyncDataRequest req = buildRequest("QTY", "update", list);
        when(rocketMQTemplate.syncSend(anyString(), anyList())).thenReturn(mock(SendResult.class));

        CommonResponse resp = service.bathSyncRmsSoData(req);
        assertEquals("success", resp.getData());

        @SuppressWarnings("unchecked")
        ArgumentCaptor<List<Message<List<RmsSyncSoDataReqDto>>>> captor = ArgumentCaptor.forClass(List.class);
        verify(rocketMQTemplate, times(1)).syncSend(eq("test-topic"), captor.capture());
        List<Message<List<RmsSyncSoDataReqDto>>> messages = captor.getValue();
        assertEquals(2, messages.size());
    }

    @Test
    @DisplayName("bathSyncRmsSoData dataList 为空抛出 BizException")
    void bathSyncRmsSoData_empty_throw() {
        RmsSyncDataRequest req = buildRequest("QTY", "update", new ArrayList<>());
        BizException ex = assertThrows(BizException.class, () -> service.bathSyncRmsSoData(req));
        assertTrue(ex.getMessage().contains("dataList exception"));
    }

    @Test
    @DisplayName("bathSyncRmsSoData 发送异常向外抛出")
    void bathSyncRmsSoData_send_throw() {
        RmsSyncDataRequest req = buildRequest("QTY", "update", Arrays.asList(mockJson(), mockJson()));
        when(rocketMQTemplate.syncSend(anyString(), anyList())).thenThrow(new RuntimeException("mq error"));
        assertThrows(RuntimeException.class, () -> service.bathSyncRmsSoData(req));
    }

    private JSONObject mockJson() {
        JSONObject obj = new JSONObject();
        obj.put("rmsId", "RMS001");
        return obj;
    }

    private RmsSyncDataRequest buildRequest(String type, String operateType, List<JSONObject> dataList) {
        RmsSyncDataRequest req = new RmsSyncDataRequest();
        req.setType(type);
        req.setOperateType(operateType);
        req.setDataList(dataList);
        req.setFields(Arrays.asList("field1", "field2"));
        return req;
    }

    private static void setField(Object target, String fieldName, Object value) throws Exception {
        Field f = target.getClass().getDeclaredField(fieldName);
        f.setAccessible(true);
        f.set(target, value);
    }
} 