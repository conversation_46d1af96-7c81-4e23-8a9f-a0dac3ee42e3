package com.mi.info.intl.retail.so.app.mq;

import com.alibaba.fastjson.JSONObject;
import com.mi.info.intl.retail.constant.CommonConstant;
import com.mi.info.intl.retail.service.DistributionLock;
import com.mi.info.intl.retail.service.DistributionLockService;
import com.mi.info.intl.retail.so.app.mq.dto.RmsSyncSoDataReqDto;
import com.mi.info.intl.retail.so.domain.datasync.RmsSyncSoDataManage;
import com.mi.info.intl.retail.so.domain.datasync.dto.StockDataSyncReqDto;
import com.mi.info.intl.retail.so.domain.datasync.entity.IntlDatasyncLog;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncDataTypeEnum;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncOperateTypeTypeEnum;
import com.mi.info.intl.retail.so.infra.database.mapper.datasync.IntlDatasyncLogMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.datasync.RmsStockDataSyncMapper;
import com.xiaomi.cnzone.commons.utils.JacksonUtil;
import org.apache.rocketmq.common.message.MessageExt;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * RmsSyncSoDataConsumer 单元测试
 *
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("RMS同步SO数据消费者测试")
class RmsSyncSoDataConsumerTest {

    @Mock
    private RmsSyncSoDataManage rmsSyncData;

    @Mock
    private IntlDatasyncLogMapper intlDatasyncLogMapper;

    @Mock
    private RmsStockDataSyncMapper rmsStockDataSyncMapper;

    @Mock
    private DistributionLockService distributionLockService;

    @Mock
    private DistributionLock distributionLock;

    @InjectMocks
    private RmsSyncSoDataConsumer consumer;

    private MessageExt messageExt;
    private RmsSyncSoDataReqDto singleReqDto;
    private List<RmsSyncSoDataReqDto> batchReqDtos;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        messageExt = new MessageExt();
        messageExt.setMsgId("test-msg-id-001");
        messageExt.setReconsumeTimes(0);

        // 构建单条请求数据
        singleReqDto = buildSingleReqDto();

        // 构建批量请求数据
        batchReqDtos = buildBatchReqDtos();
    }

    @Test
    @DisplayName("正常处理单条IMEI数据同步消息")
    void testOnMessage_SingleImeiData_Success() throws Exception {
        // 准备测试数据
        String bodyJson = JacksonUtil.toStr(Collections.singletonList(singleReqDto));
        messageExt.setBody(bodyJson.getBytes("UTF-8"));

        // Mock 分布式锁服务
        when(distributionLockService.tryLockNoArgs(any(String.class)))
                .thenReturn(distributionLock);

        // 执行测试
        consumer.onMessage(messageExt);

        // 验证调用 - 使用 any() 进行宽松匹配
        verify(rmsSyncData).saveDb(any(RmsSyncSoDataReqDto.class));
        verify(distributionLockService).tryLockNoArgs(any(String.class));
        verify(distributionLock).close();
    }

    @Test
    @DisplayName("正常处理批量IMEI数据同步消息")
    void testOnMessage_BatchImeiData_Success() throws Exception {
        // 准备测试数据
        String bodyJson = JacksonUtil.toStr(batchReqDtos);
        messageExt.setBody(bodyJson.getBytes("UTF-8"));

        // Mock 分布式锁服务
        when(distributionLockService.tryLockNoArgs(any(String.class)))
                .thenReturn(distributionLock);

        // 执行测试
        consumer.onMessage(messageExt);

        // 验证调用 - 使用 anyList() 进行宽松匹配
        verify(rmsSyncData).saveBatchDb(anyList());
        verify(distributionLockService).tryLockNoArgs(any(String.class));
        verify(distributionLock).close();
    }

    @Test
    @DisplayName("处理单条数据同步失败时记录错误日志")
    void testOnMessage_SingleDataSync_Failure_RecordsErrorLog() throws Exception {
        // 准备测试数据
        String bodyJson = JacksonUtil.toStr(Collections.singletonList(singleReqDto));
        messageExt.setBody(bodyJson.getBytes("UTF-8"));

        // Mock 分布式锁服务
        when(distributionLockService.tryLockNoArgs(any(String.class)))
                .thenReturn(distributionLock);

        // Mock 同步服务抛出异常
        doThrow(new RuntimeException("同步失败"))
                .when(rmsSyncData).saveDb(any(RmsSyncSoDataReqDto.class));

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> consumer.onMessage(messageExt));

        // 验证错误日志记录
        verify(intlDatasyncLogMapper).insert(any(IntlDatasyncLog.class));
    }

    @Test
    @DisplayName("处理批量数据同步失败时记录错误日志")
    void testOnMessage_BatchDataSync_Failure_RecordsErrorLog() throws Exception {
        // 准备测试数据
        String bodyJson = JacksonUtil.toStr(batchReqDtos);
        messageExt.setBody(bodyJson.getBytes("UTF-8"));

        // Mock 分布式锁服务
        when(distributionLockService.tryLockNoArgs(any(String.class)))
                .thenReturn(distributionLock);

        // Mock 批量同步服务抛出异常
        doThrow(new RuntimeException("批量同步失败"))
                .when(rmsSyncData).saveBatchDb(anyList());

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> consumer.onMessage(messageExt));

        // 验证每个请求都记录了错误日志
        verify(intlDatasyncLogMapper, times(batchReqDtos.size())).insert(any(IntlDatasyncLog.class));
    }

    @Test
    @DisplayName("处理存量数据同步失败时更新同步状态")
    void testOnMessage_StockDataSync_Failure_UpdatesSyncStatus() throws Exception {
        // 准备存量数据测试
        RmsSyncSoDataReqDto stockReqDto = buildStockDataReqDto();
        String bodyJson = JacksonUtil.toStr(Collections.singletonList(stockReqDto));
        messageExt.setBody(bodyJson.getBytes("UTF-8"));

        // Mock 分布式锁服务
        when(distributionLockService.tryLockNoArgs(any(String.class)))
                .thenReturn(distributionLock);

        // Mock 同步服务抛出异常
        doThrow(new RuntimeException("存量同步失败"))
                .when(rmsSyncData).saveDb(any(RmsSyncSoDataReqDto.class));

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> consumer.onMessage(messageExt));

        // 验证存量数据同步状态更新
        verify(rmsStockDataSyncMapper).updateImeiSyncStatus(any(StockDataSyncReqDto.class));
    }

    @Test
    @DisplayName("处理消息体编码异常时抛出RuntimeException")
    void testOnMessage_EncodingException_ThrowsRuntimeException() {
        // 准备无效字节序列（会导致UTF-8解码失败）
        byte[] invalidBytes = new byte[] {(byte) 0xFF, (byte) 0xFE, (byte) 0xFD};
        messageExt.setBody(invalidBytes);

        // 执行测试并验证异常
        assertThrows(RuntimeException.class,
                () -> consumer.onMessage(messageExt));

    }

    @Test
    @DisplayName("处理空数据列表时正常处理")
    void testOnMessage_EmptyDataList_Success() throws Exception {
        // 准备空数据列表
        String bodyJson = JacksonUtil.toStr(Collections.emptyList());
        messageExt.setBody(bodyJson.getBytes("UTF-8"));

        // 执行测试
        // 执行测试并验证异常
        assertThrows(RuntimeException.class,
                () -> consumer.onMessage(messageExt));

        // 验证没有调用同步服务
        verify(rmsSyncData, never()).saveDb(any(RmsSyncSoDataReqDto.class));
        verify(rmsSyncData, never()).saveBatchDb(anyList());
    }

    @Test
    @DisplayName("处理重试消息时正常处理")
    void testOnMessage_RetryMessage_Success() throws Exception {
        // 设置重试次数
        messageExt.setReconsumeTimes(2);
        String bodyJson = JacksonUtil.toStr(Collections.singletonList(singleReqDto));
        messageExt.setBody(bodyJson.getBytes("UTF-8"));

        // Mock 分布式锁服务
        when(distributionLockService.tryLockNoArgs(any(String.class)))
                .thenReturn(distributionLock);

        // 执行测试
        consumer.onMessage(messageExt);

        // 验证正常处理 - 使用 any() 进行宽松匹配
        verify(rmsSyncData).saveDb(any(RmsSyncSoDataReqDto.class));
    }

    @Test
    @DisplayName("记录同步日志时处理空请求数据")
    void testRecordSyncLog_NullRequestDto_SkipsLogging() throws Exception {
        // 通过反射调用私有方法
        java.lang.reflect.Method method = RmsSyncSoDataConsumer.class
                .getDeclaredMethod("recordSyncLog", RmsSyncSoDataReqDto.class, String.class);
        method.setAccessible(true);

        // 执行测试
        method.invoke(consumer, (RmsSyncSoDataReqDto) null, "測試錯誤");

        // 验证没有记录日志
        verify(intlDatasyncLogMapper, never()).insert(any(IntlDatasyncLog.class));
    }

    @Test
    @DisplayName("记录同步日志时处理空数据对象")
    void testRecordSyncLog_NullData_SkipsLogging() throws Exception {
        // 准备空数据的请求
        RmsSyncSoDataReqDto reqDto = new RmsSyncSoDataReqDto();
        reqDto.setData(null);
        reqDto.setType("imei");
        reqDto.setOperateType("create");

        // 通过反射调用私有方法
        java.lang.reflect.Method method = RmsSyncSoDataConsumer.class
                .getDeclaredMethod("recordSyncLog", RmsSyncSoDataReqDto.class, String.class);
        method.setAccessible(true);

        // 执行测试
        method.invoke(consumer, reqDto, "測試錯誤");

        // 验证没有记录日志
        verify(intlDatasyncLogMapper, never()).insert(any(IntlDatasyncLog.class));
    }

    @Test
    @DisplayName("解析零售商ID时处理无效格式")
    void testParseRetailId_InvalidFormat_ReturnsZero() throws Exception {
        // 通过反射调用私有方法
        java.lang.reflect.Method method = RmsSyncSoDataConsumer.class
                .getDeclaredMethod("parseRetailId", String.class);
        method.setAccessible(true);

        // 测试无效格式
        Long result = (Long) method.invoke(consumer, "invalid-id");

        // 验证返回0
        assertEquals(0L, result);
    }

    @Test
    @DisplayName("解析零售商id时处理空值")
    void testParseRetailId_NullValue_ReturnsZero() throws Exception {
        // 通过反射调用私有方法
        java.lang.reflect.Method method = RmsSyncSoDataConsumer.class
                .getDeclaredMethod("parseRetailId", String.class);
        method.setAccessible(true);

        // 测试空值
        Long result = (Long) method.invoke(consumer, (String) null);

        // 验证返回0
        assertEquals(0L, result);
    }

    @Test
    @DisplayName("解析操作类型时处理空枚举")
    void testResolveOperateType_NullEnum_ReturnsDefaultValue() throws Exception {
        // 通过反射调用私有方法
        java.lang.reflect.Method method = RmsSyncSoDataConsumer.class
                .getDeclaredMethod("resolveOperateType", DataSyncOperateTypeTypeEnum.class);
        method.setAccessible(true);

        // 测试空枚举
        Integer result = (Integer) method.invoke(consumer, (DataSyncOperateTypeTypeEnum) null);

        // 验证返回默认值
        assertEquals(CommonConstant.DEFAULT_INTEGER, result);
    }

    @Test
    @DisplayName("处理存量同步失败失败时处理空枚举")
    void testHandleStockSyncFailure_NullEnum_SkipsHandling() throws Exception {
        // 准备测试数据
        JSONObject data = new JSONObject();
        data.put("idNew", 123L);

        //通过反射调用私有方法
        java.lang.reflect.Method method = RmsSyncSoDataConsumer.class
                .getDeclaredMethod("handleStockSyncFailure", JSONObject.class, DataSyncDataTypeEnum.class);
        method.setAccessible(true);

        // 执行测试
        method.invoke(consumer, data, null);

        // 验证没有更新同步状态
        verify(rmsStockDataSyncMapper, never()).updateImeiSyncStatus(any(StockDataSyncReqDto.class));
        verify(rmsStockDataSyncMapper, never()).updateQtySyncStatus(any(StockDataSyncReqDto.class));
    }

    @Test
    @DisplayName("处理存量同步失败时处理失败id")
    void testHandleStockSyncFailure_MissingId_SkipsHandling() throws Exception {
        // 准备测试数据
        JSONObject data = new JSONObject();
        // 不设置idNew字段

        // 通过反射调用私有方法
        java.lang.reflect.Method method = RmsSyncSoDataConsumer.class
                .getDeclaredMethod("handleStockSyncFailure", JSONObject.class, DataSyncDataTypeEnum.class);
        method.setAccessible(true);

        // 执行测试
        method.invoke(consumer, data, DataSyncDataTypeEnum.IMEI);

        // 验证没有更新同步状态
        verify(rmsStockDataSyncMapper, never()).updateImeiSyncStatus(any(StockDataSyncReqDto.class));
    }

    /**
     * 构建单条请求数据
     */
    private RmsSyncSoDataReqDto buildSingleReqDto() {
        JSONObject data = new JSONObject();
        // 基本信息
        data.put("id", 1001L);
        data.put("sn", "SN123456789");
        data.put("imei1", "123456789012345");
        data.put("imei2", "123456789012346");
        data.put("snMask", "SN***6789");
        data.put("imei1Mask", "123***012345");
        data.put("imei2Mask", "123***012346");
        data.put("snHash", "hash123456789");
        data.put("imeiFromHub", "0");
        data.put("productCode", "MI14");

        // 组织信息
        data.put("orgInfoId", 1001L);
        data.put("userInfoId", 2001L);
        data.put("focusModel", "1");
        data.put("imeiRuleId", "rule001");
        data.put("imeiRuleBefore", 5);
        data.put("imeiRuleAfter", 3);
        data.put("imeiRuleIsActivingCheck", true);

        // 激活相关
        data.put("activationVerificationTime", 1756102454372L);
        data.put("activationTime", 1756016054372L);
        data.put("activationFrequency", 1);
        data.put("activationSite", "Beijing");

        // 验证相关
        data.put("repeatUser", "user001");
        data.put("lastMd", "MD001");
        data.put("currency", "USD");
        data.put("verifyingState", 1);
        data.put("verificationResult", 1);
        data.put("verifyResult", 1);
        data.put("verifyResultDetail", "验证通过");
        data.put("failedReason", 0);
        data.put("failedReasonDetail", 0);
        data.put("siVerifyResult", 1);

        // 上报和账户信息
        data.put("reportingType", *********);
        data.put("firstLevelAccountCode", "ACC001");
        data.put("finalSalesCountry", "CN");
        data.put("allowSalesCountry", "CN,US");
        data.put("note", "测试数据");

        // 价格信息
        data.put("rrpCode", "RRP001");
        data.put("rrp", 999.99);
        data.put("batchId", 3001L);
        data.put("salesTime", 1755929654372L);

        // 创建和修改信息
        data.put("createdBy", 4001L);
        data.put("createdOn", 1755843254372L);
        data.put("modifiedBy", 4001L);
        data.put("modifiedOn", 1756016054372L);
        data.put("status", 0);

        // 门店和位置信息
        data.put("storeRmsCode", "STORE001");
        data.put("supplierCode", "SUP001");
        data.put("positionRmsCode", "POS001");
        data.put("rmsId", "RMS001");
        data.put("retailId", "123");

        // 其他信息
        data.put("isPhotoExist", 1);
        data.put("dataFrom", 1);
        data.put("salesmanMid", 5001L);
        data.put("detailId", "DETAIL001");
        data.put("salesChannel", 6001L);
        data.put("batchIdStr", "batch-uuid-001");
        data.put("stockId", 7001L);

        // 存量数据标识
        data.put("isStockData", 0);
        data.put("idNew", 456L);

        // 扩展字段（RmsSyncImeiData特有）
        data.put("retailerCode", "RET001");
        data.put("verifyResult", 1);
        data.put("reportType", *********);
        data.put("createdTime", 1755843254372L);
        data.put("modifiedon", 1756016054372L);
        data.put("modifiedbyMiId", "4001");
        data.put("storeCodeRMS", "STORE001");
        data.put("positionCodeRMS", "POS001");
        data.put("createdbyMiid", "4001");
        data.put("salesManMiid", "5001");
        data.put("storeCodeNew", "STORE_NEW001");
        data.put("positionCodeNew", "POS_NEW001");
        data.put("repeatUserDetail", "user001");

        RmsSyncSoDataReqDto reqDto = new RmsSyncSoDataReqDto();
        reqDto.setData(data);
        reqDto.setType("imei");
        reqDto.setOperateType("create");

        List<String> expectedFields = Arrays.asList(
                "imeiRuleIsActivingCheck", "imeiRuleBefore", "imeiRuleAfter",
                "activationVerificationTime", "verifyingState", "activationTime",
                "activationFrequency", "activationSite", "siVerifyResult",
                "verifyResult", "verifyResultDetail", "repeatUserDetail",
                "lastMd", "finalSalesCountry", "failedReason",
                "failedReasonDetail", "firstLevelAccountCode"
        );

        reqDto.setFields(expectedFields);

        return reqDto;
    }

    /**
     * 构建批量请求数据
     */
    private List<RmsSyncSoDataReqDto> buildBatchReqDtos() {
        RmsSyncSoDataReqDto reqDto1 = buildSingleReqDto();

        RmsSyncSoDataReqDto reqDto2 = new RmsSyncSoDataReqDto();
        JSONObject data2 = new JSONObject();
        // 基本信息
        data2.put("id", 1002L);
        data2.put("sn", "SN123456790");
        data2.put("imei1", "***************");
        data2.put("imei2", "***************");
        data2.put("snMask", "SN***6790");
        data2.put("imei1Mask", "123***012347");
        data2.put("imei2Mask", "123***012348");
        data2.put("snHash", "hash123456790");
        data2.put("imeiFromHub", "0");
        data2.put("productCode", "MI14");

        // 组织信息
        data2.put("orgInfoId", 1002L);
        data2.put("userInfoId", 2002L);
        data2.put("focusModel", "1");
        data2.put("imeiRuleId", "rule002");
        data2.put("imeiRuleBefore", 5);
        data2.put("imeiRuleAfter", 3);
        data2.put("imeiRuleIsActivingCheck", true);

        // 激活相关
        data2.put("activationVerificationTime", 1756102454373L);
        data2.put("activationTime", 1756016054373L);
        data2.put("activationFrequency", 1);
        data2.put("activationSite", "Shanghai");

        // 验证相关
        data2.put("repeatUser", "user002");
        data2.put("lastMd", "MD002");
        data2.put("currency", "USD");
        data2.put("verifyingState", 1);
        data2.put("verificationResult", 1);
        data2.put("verifyResult", 1);
        data2.put("verifyResultDetail", "验证通过");
        data2.put("failedReason", 0);
        data2.put("failedReasonDetail", 0);
        data2.put("siVerifyResult", 1);

        // 上报和账户信息
        data2.put("reportingType", *********);
        data2.put("firstLevelAccountCode", "ACC002");
        data2.put("finalSalesCountry", "CN");
        data2.put("allowSalesCountry", "CN,US");
        data2.put("note", "测试数据2");

        // 价格信息
        data2.put("rrpCode", "RRP002");
        data2.put("rrp", 899.99);
        data2.put("batchId", 3002L);
        data2.put("salesTime", 1755929654373L);

        // 创建和修改信息
        data2.put("createdBy", 4002L);
        data2.put("createdOn", 1755843254373L);
        data2.put("modifiedBy", 4002L);
        data2.put("modifiedOn", 1756016054373L);
        data2.put("status", 0);

        // 门店和位置信息
        data2.put("storeRmsCode", "STORE002");
        data2.put("supplierCode", "SUP002");
        data2.put("positionRmsCode", "POS002");
        data2.put("rmsId", "RMS002");
        data2.put("retailId", "124");

        // 其他信息
        data2.put("isPhotoExist", 1);
        data2.put("dataFrom", 1);
        data2.put("salesmanMid", 5002L);
        data2.put("detailId", "DETAIL002");
        data2.put("salesChannel", 6002L);
        data2.put("batchIdStr", "batch-uuid-002");
        data2.put("stockId", 7002L);

        // 存量数据标识
        data2.put("isStockData", 0);
        data2.put("idNew", 457L);

        // 扩展字段（RmsSyncImeiData特有）
        data2.put("retailerCode", "RET002");
        data2.put("verifyResult", 1);
        data2.put("reportType", *********);
        data2.put("createdTime", 1755843254373L);
        data2.put("modifiedon", 1756016054373L);
        data2.put("modifiedbyMiId", "4002");
        data2.put("storeCodeRMS", "STORE002");
        data2.put("positionCodeRMS", "POS002");
        data2.put("createdbyMiid", "4002");
        data2.put("salesManMiid", "5002");
        data2.put("storeCodeNew", "STORE_NEW002");
        data2.put("positionCodeNew", "POS_NEW002");
        data2.put("repeatUserDetail", "user002");

        reqDto2.setData(data2);
        reqDto2.setType("imei");
        reqDto2.setOperateType("create");
        reqDto2.setFields(Arrays.asList(
                "imeiRuleIsActivingCheck", "imeiRuleBefore", "imeiRuleAfter",
                "activationVerificationTime", "verifyingState", "activationTime",
                "activationFrequency", "activationSite", "siVerifyResult",
                "verifyResult", "verifyResultDetail", "repeatUserDetail",
                "lastMd", "finalSalesCountry", "failedReason", "failedReasonDetail",
                "firstLevelAccountCode"
        ));

        return Arrays.asList(reqDto1, reqDto2);
    }

    /**
     * 构建存量数据请求
     */
    private RmsSyncSoDataReqDto buildStockDataReqDto() {
        JSONObject data = new JSONObject();
        // 基本信息
        data.put("id", 1003L);
        data.put("sn", "SN123456791");
        data.put("imei1", "***************");
        data.put("imei2", "***************");
        data.put("snMask", "SN***6791");
        data.put("imei1Mask", "123***012349");
        data.put("imei2Mask", "123***012350");
        data.put("snHash", "hash123456791");
        data.put("imeiFromHub", "0");
        data.put("productCode", "MI14");

        // 组织信息
        data.put("orgInfoId", 1003L);
        data.put("userInfoId", 2003L);
        data.put("focusModel", "1");
        data.put("imeiRuleId", "rule003");
        data.put("imeiRuleBefore", 5);
        data.put("imeiRuleAfter", 3);
        data.put("imeiRuleIsActivingCheck", true);

        // 激活相关
        data.put("activationVerificationTime", 1756102454374L);
        data.put("activationTime", 1756016054374L);
        data.put("activationFrequency", 1);
        data.put("activationSite", "Guangzhou");

        // 验证相关
        data.put("repeatUser", "user003");
        data.put("lastMd", "MD003");
        data.put("currency", "USD");
        data.put("verifyingState", 1);
        data.put("verificationResult", 1);
        data.put("verifyResult", 1);
        data.put("verifyResultDetail", "验证通过");
        data.put("failedReason", 0);
        data.put("failedReasonDetail", 0);
        data.put("siVerifyResult", 1);

        // 上报和账户信息
        data.put("reportingType", *********);
        data.put("firstLevelAccountCode", "ACC003");
        data.put("finalSalesCountry", "CN");
        data.put("allowSalesCountry", "CN,US");
        data.put("note", "存量测试数据");

        // 价格信息
        data.put("rrpCode", "RRP003");
        data.put("rrp", 799.99);
        data.put("batchId", 3003L);
        data.put("salesTime", 1755929654374L);

        // 创建和修改信息
        data.put("createdBy", 4003L);
        data.put("createdOn", 1755843254374L);
        data.put("modifiedBy", 4003L);
        data.put("modifiedOn", 1756016054374L);
        data.put("status", 0);

        // 门店和位置信息
        data.put("storeRmsCode", "STORE003");
        data.put("supplierCode", "SUP003");
        data.put("positionRmsCode", "POS003");
        data.put("rmsId", "RMS003");
        data.put("retailId", "125");

        // 其他信息
        data.put("isPhotoExist", 1);
        data.put("dataFrom", 1);
        data.put("salesmanMid", 5003L);
        data.put("detailId", "DETAIL003");
        data.put("salesChannel", 6003L);
        data.put("batchIdStr", "batch-uuid-003");
        data.put("stockId", 7003L);

        // 存量数据标识
        data.put("isStockData", 1); // 存量數據
        data.put("idNew", 458L);

        // 扩展字段（RmsSyncImeiData特有）
        data.put("retailerCode", "RET003");
        data.put("verifyResult", 1);
        data.put("reportType", *********);
        data.put("createdTime", 1755843254374L);
        data.put("modifiedon", 1756016054374L);
        data.put("modifiedbyMiId", "4003");
        data.put("storeCodeRMS", "STORE003");
        data.put("positionCodeRMS", "POS003");
        data.put("createdbyMiid", "4003");
        data.put("salesManMiid", "5003");
        data.put("storeCodeNew", "STORE_NEW003");
        data.put("positionCodeNew", "POS_NEW003");
        data.put("repeatUserDetail", "user003");

        RmsSyncSoDataReqDto reqDto = new RmsSyncSoDataReqDto();
        reqDto.setData(data);
        reqDto.setType("imei");
        reqDto.setOperateType("create");
        reqDto.setFields(Arrays.asList(
                "imeiRuleIsActivingCheck", "imeiRuleBefore", "imeiRuleAfter",
                "activationVerificationTime", "verifyingState", "activationTime",
                "activationFrequency", "activationSite", "siVerifyResult",
                "verifyResult", "verifyResultDetail", "repeatUserDetail",
                "lastMd", "finalSalesCountry", "failedReason", "failedReasonDetail",
                "firstLevelAccountCode"
        ));

        return reqDto;
    }
}