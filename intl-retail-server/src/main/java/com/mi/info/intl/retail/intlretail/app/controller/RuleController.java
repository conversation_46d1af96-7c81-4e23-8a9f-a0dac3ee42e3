package com.mi.info.intl.retail.intlretail.app.controller;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import org.springframework.web.bind.annotation.*;

import com.alibaba.druid.util.StringUtils;
import com.mi.info.intl.retail.api.so.rule.RuleServiceFactory;
import com.mi.info.intl.retail.api.so.rule.req.GetRetailerSoRuleReq;
import com.mi.info.intl.retail.api.so.rule.resp.GetRetailRuleResp;
import com.mi.info.intl.retail.api.so.rule.service.IntlRuleRetailerApiService;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDoc;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;
import com.xiaomi.mone.docs.annotations.http.MiApiRequestMethod;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping("/api/rule/v1")
@ApiModule(value = "国际新零售平台", apiInterface = RuleController.class)
public class RuleController extends BaseController {

    @Resource
    private RuleServiceFactory ruleServiceFactory;

    /**
     * 通用接口：根据请求类型返回不同类型的retail信息
     *
     * @param request 要求
     * @return {@link CommonApiResponse }<{@link GetRetailRuleResp }>
     */
    @PostMapping("/queryCountryRules")
    @ResponseBody
    @ApiDoc(description = "根据请求类型返回不同类型的retail信息", value = "/api/rule/v1/queryCountryRules",
        method = MiApiRequestMethod.POST)
    public CommonApiResponse<GetRetailRuleResp> queryCountryRules(@RequestBody @NotNull GetRetailerSoRuleReq request) {
        String type = ruleServiceFactory.getType(request.getType());
        IntlRuleRetailerApiService apiService = ruleServiceFactory.getService(type);
        if (null == apiService || StringUtils.isEmpty(type)) {
            return new CommonApiResponse<>(null);
        }
        return new CommonApiResponse<>(apiService.queryCountryRules(request));
    }
}
