package com.mi.info.intl.retail.intlretail.app.remote.service;

import com.mi.info.intl.retail.intlretail.service.api.bpm.BpmService;
import com.mi.info.intl.retail.intlretail.service.api.bpm.dto.BpmDetail;
import com.mi.info.intl.retail.intlretail.service.api.bpm.dto.FormDataEntity;
import com.mi.info.intl.retail.intlretail.service.api.bpm.enums.BpmApproveBusinessCodeEnum;
import com.mi.info.intl.retail.utils.JsonUtils;
import com.mi.oa.infra.oaucf.bpm.rep.CreateProcInstResp;
import com.mi.oa.infra.oaucf.bpm.rep.QueryProcInstResp;
import com.mi.oa.infra.oaucf.bpm.req.CreateProcInstReq;
import com.mi.oa.infra.oaucf.bpm.req.RecallProcInstReq;
import com.mi.oa.infra.oaucf.bpm.req.ReturnAfterSubmitReq;
import com.mi.oa.infra.oaucf.bpm.req.TerminateProcInstReq;
import com.mi.oa.infra.oaucf.bpm.service.ApprovalService;
import com.mi.oa.infra.oaucf.bpm.service.FormInstanceService;
import com.mi.oa.infra.oaucf.bpm.service.ProcessInstanceService;
import com.mi.oa.infra.oaucf.core.dto.BaseResp;
import com.xiaomi.mit.common.json.Jacksons;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.UUID;

@Service
@Slf4j

public class BpmServiceImpl implements BpmService {

    @Autowired
    protected ApplicationContext applicationContext;

    @Autowired
    private ProcessInstanceService processInstanceService;

    @Autowired
    private ApprovalService approvalService;

    @Autowired
    private FormInstanceService formInstanceService;

    //BPM_CREATE_LOG:BaseResp(code=0, message=success, data=CreateProcInstResp(businessKey=efb3095a6d734b76b408ca98cf72a52a),
    // logId=4631fde1d0f1d774e6d6018b909d970d, traceId=4789133d48a6014fef4d8f41e189ba65)
    @Override
    public String create(FormDataEntity formDataEntity, BpmApproveBusinessCodeEnum businessCode, String emailPrefix,
                         String businessKey, Map<String, Object> variables) {
        CreateProcInstReq createProcInstReq = CreateProcInstReq.builder()
                .modelCode(businessCode.getModelCode())
                .processInstanceName(businessCode.getInstanceName())
                .businessKey(StringUtils.isEmpty(businessKey) ?
                        UUID.randomUUID().toString().replace("-", "") : businessKey)
                .startUserId(StringUtils.isEmpty(emailPrefix) ? businessCode.getEmailPrefix() : emailPrefix)
                .formData(Jacksons.BASE.parseMap(Jacksons.BASE.toJson(formDataEntity), String.class, Object.class))
                .variables(variables == null ? null :
                        Jacksons.BASE.parseMap(Jacksons.BASE.toJson(variables), String.class, Object.class))
                .build();
        BaseResp<CreateProcInstResp> resp = processInstanceService.create(createProcInstReq);
        log.error("BPM_CREATE_LOG:{}", JsonUtils.toStr(resp));
        if (resp.getCode() != 0) {
            throw new RuntimeException(resp.getMessage());
        }
        return JsonUtils.toStr(createProcInstReq);
    }

    @Override
    public void terminate(String businessKey, String emailPrefix, String comment) {
        TerminateProcInstReq terminateProcInstReq = TerminateProcInstReq.builder()
                .businessKey(businessKey)
                .operator(emailPrefix)
                .comment(comment)
                .build();
        BaseResp<Void> resp = processInstanceService.terminate(terminateProcInstReq);
        log.info("BPM_TERMINATE_LOG:{}", resp.toString());
        if (resp.getCode() != 0) {
            throw new RuntimeException(resp.getMessage());
        }
    }

    @Override
    public void recall(String businessKey, String emailPrefix, String comment) {
        RecallProcInstReq req = RecallProcInstReq.builder()
                .businessKey(businessKey)
                .operator(emailPrefix)
                .comment(comment)
                .build();
        BaseResp<Void> resp = processInstanceService.recall(req);
        log.info("BPM_TERMINATE_LOG:{}", resp.toString());
        if (resp.getCode() != 0) {
            throw new RuntimeException(resp.getMessage());
        }
    }

    @Override
    public BpmDetail detail(String businessKey) {
        BaseResp<QueryProcInstResp> resp = processInstanceService.get(businessKey, "");
        log.info("BPM_DETAIL_LOG:{}", resp.toString());
        if (resp.getCode() != 0) {
            throw new RuntimeException(resp.getMessage());
        }
        QueryProcInstResp data = resp.getData();
        BpmDetail bpmDetail = new BpmDetail();
        bpmDetail.setBusinessKey(data.getBusinessKey());
        bpmDetail.setProcessDefinitionId(data.getProcessDefinitionId());
        bpmDetail.setProcessDefinitionName(data.getProcessDefinitionName());
        bpmDetail.setProcessDefinitionVersion(data.getProcessDefinitionVersion());
        bpmDetail.setProcessInstanceId(data.getProcessInstanceId());
        bpmDetail.setProcessInstanceName(data.getProcessInstanceName());
        return bpmDetail;
    }

    @Override
    public void submit(String businessKey, String emailPrefix, FormDataEntity formDataEntity,
                       Map<String, Object> variables) {

        String taskId = formInstanceService.queryFormInst(businessKey, "").getData().getTaskId();
        if (taskId == null) {
            throw new RuntimeException("businessKey not found :" + businessKey);
        }
        ReturnAfterSubmitReq req = new ReturnAfterSubmitReq();
        req.setTaskId(taskId);
        req.setOperator(emailPrefix);
        if (formDataEntity != null) {
            req.setFormData(Jacksons.BASE.parseMap(Jacksons.BASE.toJson(formDataEntity), String.class, Object.class));
        }
        if (variables != null) {
            req.setVariables(Jacksons.BASE.parseMap(Jacksons.BASE.toJson(variables), String.class, Object.class));
        }
        BaseResp<Void> resp = approvalService.submit(req);
        if (resp.getCode() != 0) {
            throw new RuntimeException(resp.getMessage());
        }
    }
}
