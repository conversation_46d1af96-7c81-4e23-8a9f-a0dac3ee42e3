env: dev
miwork:
  alarm:
    groupId:
      p0: oc_1e6d1e54ac5eae84730fe55e86a013e7
      p1: oc_1e6d1e54ac5eae84730fe55e86a013e7
      p2: oc_1e6d1e54ac5eae84730fe55e86a013e7

# 加密配置
itsm:
  key@kc-sid: india-sales.g
  key: GCC_dXFDqR6UmNulwNx8LZrw1JMK68LlB40ceJKmJ4lBNRgSjrf6mb3rR4WDzaVZn7xc87__GBCdCPLL-apOlYw2Jxx1ShexGBQTxLIzWvxHDHwVl2mGl9xX5JTRogA
  aesGcmKey@kc-sid: india-sales.g
  aesGcmKey: GDCtHFFCXnxDA6frlNh0r98fRUrntsw1EtJeJWkEl2NZ0hMwM0pqK8IU/ioff3qbp1oYEjA1pL7oHEv5jeTSidAun/um/xgQdaL4VXQYSOuyQr+a8zZtgRgUHCiGZMhXytwtIiRb+akikk4IjiQA

feishu:
  appId: cli_a60d6cea8478d063
  appSecret@kc-sid: india-sales.g
  appSecret: GDDNlEYkzhoTik1dVyPqlcWPXVuogQbNfUiP2XLy9yAcGh5KLYgoMC1c6EzmjJeTYzAYEqJPvtpFFkOqmbN025peIFY0/xgQcfNLEUowQH+GXGG8jS8HjBgUOfevm5PGoheSWZsXTgWMPCbIiUUA
  getGroupListUrl: https://open.f.mioffice.cn/open-apis/im/v1/chats?page_token=
  getUnionidsUrl: https://open.f.mioffice.cn/open-apis/contact/v3/users/batch_get_id
  sendMessageGroupUrl: https://open.f.mioffice.cn/open-apis/message/v4/send/
  batchSendMessageUrl: https://open.f.mioffice.cn/open-apis/message/v4/batch_send/
  tokenUrl: https://open.f.mioffice.cn/open-apis/auth/v3/tenant_access_token/internal
  messageUrl: https://open.f.mioffice.cn/open-apis/im/v1/messages

oaucf:
  auth:
    appId: dthJf4KwiRL6          # 应用的id
    appSecret: mX0P9kaZqG2yN8Rm  # 应用的secret
  bpm:
    enabled: true
    url: https://bpm-infra.test.mioffice.cn/runtime    # api的url
    connectTimeout: 10          # 连接超时时间 单位秒
    readTimeout: 60

deployment:
  platform:
    source: mione